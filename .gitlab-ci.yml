# stages:
#   - build

# variables:
#   ANDROID_SDK_ROOT: "/opt/android-sdk"
#   ANDROID_HOME: "/opt/android-sdk"
#   JAVA_HOME: "/usr/lib/jvm/java-17-openjdk-amd64"

# before_script:
#     #─────────────────────────  Node 22  ─────────────────────────
#     - curl -fsSL https://deb.nodesource.com/setup_22.x | bash -
#     - apt-get install -y nodejs
#     - node -v

#     #─────────────────────────  Java 17  ─────────────────────────
#     - apt-get update -qq && apt-get install -y openjdk-17-jdk wget unzip zip
#     - java -version

#     #─────────────────────  Android SDK CLI tools  ───────────────
#     - mkdir -p $ANDROID_SDK_ROOT/cmdline-tools
#     - cd /tmp
#     - wget https://dl.google.com/android/repository/commandlinetools-linux-11076708_latest.zip -O sdk.zip
#     - unzip -q sdk.zip
#     - mkdir -p $ANDROID_SDK_ROOT/cmdline-tools/latest
#     - mv cmdline-tools $ANDROID_SDK_ROOT/cmdline-tools/latest/

#     # Add sdkmanager to PATH only once
#     - export PATH=$ANDROID_SDK_ROOT/cmdline-tools/latest/cmdline-tools/bin:$ANDROID_SDK_ROOT/platform-tools:$JAVA_HOME/bin:$PATH

#     #──────────────  Accept licences (ignore buggy exit-code)  ──────────────
#     - yes | sdkmanager --sdk_root=$ANDROID_SDK_ROOT --licenses || true

#     #──────────────  Install build-tools / platform-tools  ────────────────
#     - yes | sdkmanager --sdk_root=$ANDROID_SDK_ROOT "platform-tools" "platforms;android-33" "build-tools;33.0.2" || true
#     - echo "Installed Android SDK packages."
#     - ls -la $ANDROID_SDK_ROOT/platform-tools
#     - pwd
#     #─────────────────────────  Build APK  ─────────────────────────

# build_apk:
#   stage: build
#   script:

    
#     # Create .env.prod and show contents
#     - cp "$ENV_PROD_FILE" .env.prod
#     - echo "Contents of .env.prod:"
#     - cp "$ENV_LOCAL_FILE" .env
#     - echo "Contents of .env:"

#     # Install dependencies
#     - npm install

#     # Build APK
#     - npm run build:android:prod # Replace with your actual build script
#   artifacts:
#     paths:
#       - android/app/build/outputs/apk/release/app-release.apk
#     expire_in: 1 week
#   only: 
#     - feature/fitness

#   environment:
#     name: non-prod



































stages:
  - build

variables:
  ANDROID_SDK_ROOT: "/opt/android-sdk"
  ANDROID_HOME: "/opt/android-sdk"
  JAVA_HOME: "/usr/lib/jvm/java-17-openjdk-amd64"

before_script:
  - pwd
  - apt-get update -qq
  - apt-get install -y openjdk-17-jdk wget unzip zip
  - curl -fsSL https://deb.nodesource.com/setup_22.x | bash -
  - apt-get install -y nodejs
  - node -v
  - java -version
  - mkdir -p $ANDROID_SDK_ROOT/cmdline-tools
  - cd /tmp
  - wget https://dl.google.com/android/repository/commandlinetools-linux-11076708_latest.zip -O sdk.zip
  - unzip -q sdk.zip
  - mkdir -p $ANDROID_SDK_ROOT/cmdline-tools/latest
  - mv cmdline-tools $ANDROID_SDK_ROOT/cmdline-tools/latest/
  - export PATH=$ANDROID_SDK_ROOT/cmdline-tools/latest/cmdline-tools/bin:$ANDROID_SDK_ROOT/platform-tools:$JAVA_HOME/bin:$PATH
  - yes | sdkmanager --sdk_root=$ANDROID_SDK_ROOT --licenses || true
  - yes | sdkmanager --sdk_root=$ANDROID_SDK_ROOT "platform-tools" "platforms;android-33" "build-tools;33.0.2" || true
  - echo "Installed Android SDK packages."
  - pwd
  - ls -la

build_apk:
  stage: build
  script:
    - cd /builds/dipv/fitness-delicut-customer-mobile-app
    - echo "$ENV_PROD_FILE" > .env.prod
    - echo "$ENV_LOCAL_FILE" > .env
    - echo "Contents of .env.prod:"
    - cat .env.prod
    - echo "Contents of .env:"
    - cat .env
    - npm install
    - npm run clean:android
    - npm run build:android:prod
    - ls -a
    - cd android/app
    - ls -a
    - cd android/app/build
  artifacts:
    paths:
      - android/app/build/outputs/apk/release/app-release.apk
    expire_in: 1 week
  only:
    - feature/fitness
  environment:
    name: non-prod

