declare module 'app-actions' {
  export type LanKeyProps = 'en' | 'ar';
  export type Lanobj = { [key in LanKeyProps]: string };
  export interface EventDataProps {
    [key: string]:
      | string
      | number
      | boolean
      | undefined
      | TrackEventData
      | TrackEventData[];
  }

  interface MealType {
    name: string;
    portioning: Portion[];
    sizes: Size[];
  }

  export type MacroGuideline = MealType[];

  export interface MasterRmsDataValueProps {
    is_live?: boolean;
    is_vegetarian?: boolean;
    order_number: number;
    id: string;
    key: string;
    display_name: string;
    internal_name: string;
    ingredients: string[];
    value: string | MacroGuideline;
    range?: {
      start: number;
      end: number;
    };
    color?: string;
  }

  export interface MasterRmsDataProps {
    _id: string;
    key:
      | 'allergens'
      | 'cuisine'
      | 'dish_type'
      | 'recipe_category'
      | 'variants'
      | 'composition_type'
      | 'cooking_complexity'
      | 'diet_type'
      | 'ingredient_category'
      | 'ingredient_master_data'
      | 'ingredient_type'
      | 'meal_category'
      | 'package_type'
      | 'plating_complexity'
      | 'spice_level'
      | 'supplier_type'
      | 'unit_of_measurement'
      | 'cutting_style'
      | 'macro_guideline'
      | 'recipe_sizes';
    label: string;
    value: MasterRmsDataValueProps[];
  }

  export interface IngredientProps {
    category_name_ref: string[];
    ingredient: string;
  }

  export interface CategoryProps {
    _id: string;
    is_live?: boolean;
    is_vegetarian: boolean;
    order_number: number;
    category_name: string;
  }

  export interface MealPreferencesValueProps {
    _id: string;
    name: string;
  }

  export interface MealPreferencesProps {
    _id: string;
    key: 'dish_type' | 'cuisine' | 'spice_level';
    label: string;
    value: MealPreferencesValueProps[];
  }

  export interface AnalyzeFoodResult {
    data?: {
      nutrient_info?: {
        detection?: {
          primary_dish?: string;
        };
        display_data?: {
          calories?: number;
          protein_g?: number;
          carbs_g?: number;
          fat_g?: number;
        };
        detailed_breakdown?: {
          portion_breakdown?: {
            ingredient: string;
            weight_grams: number;
          }[];
        };
      };
    };
  }

  export interface TextMealDetectionResult {
    success?: boolean;
    warning?: string;
    data?: {
      nutrient_info?: {
        detection?: {
          primary_dish?: string;
        };
        display_data?: {
          calories?: number;
          protein_g?: number;
          carbs_g?: number;
          fat_g?: number;
        };
        detailed_breakdown?: {
          portion_breakdown?: {
            ingredient: string;
            weight_grams: number;
          }[];
        };
        confidence_metrics?: {
          overall_confidence?: number;
        };
      };
    };
    processing_time_ms?: number;
  }

  export interface AppSliceProps {
    isKeybordshow: boolean;
    loader: boolean;
    notificationMessage: string | { title: string; body: string } | null;
    masterRmsData: MasterRmsDataProps[];
    masterData?: {
      instruction?: Array<{
        id: string;
        name: string;
        name_tl: {
          ar: string;
          en: string;
        };
      }>;
    };
    masterIng?: {
      ingredient: IngredientProps[];
      category: CategoryProps[];
      ingByCat: {
        [key: string]: string[];
      };
      cusine?: Array<{ name: string }>;
      ethnicity?: string[];
    };
    getMealPreferencesData: MealPreferencesProps[];
    deliveryArea: string[];
    deliverySlots: Array<{ timing: string }>;
    cityList: Array<{ _id: string; city_name: string }>;
    lanKey: LanKeyProps;
    isRTL: boolean;
    analyzeFoodResult: AnalyzeFoodResult | null;
    textMealDetectionResult: TextMealDetectionResult | null;

  }
}
