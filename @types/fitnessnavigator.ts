import { NavigationProp } from '@react-navigation/native';
import { RootFitnessStackParamList } from './navigation';
import { ThemeProps } from './theme';

// navigationTypes.ts
type DefaultProps = {
  theme: ThemeProps;
  navigation: NavigationProp<RootFitnessStackParamList>;
};
export type FitnessRootStackParamList = {
  Tabs: undefined;
  Capture: undefined;
  Mealdata: DefaultProps & {
    photoUri: string;
    fromTextAnalysis?: boolean;
    textDescription?: string;
    selectedMealType?: string;
    analysisResult?: any; // Add this for passing the API result directly
  };
};
}

export type FitnessTabParamList = {
  Home: undefined;
  Log: undefined;
  Capture: undefined;
  Coach: undefined;
  Profile: undefined;
};
