import {
  BottomTabNavigationProp,
  BottomTabScreenProps,
} from '@react-navigation/bottom-tabs';
import { RouteProp } from '@react-navigation/native';
import { StackNavigationOptions } from '@react-navigation/stack';
import { ThemeProps } from './theme';

// Define the type for your RootTabParamList
type RootTabParamList = {
  Main: RootStackParamList;
};
type RootStackParamList = {
  MealPlan: undefined;
  AddNewAddress: undefined;
  OrderSummary: undefined;
  Account: undefined;
  Settings: undefined;
  DeliveryDetails: undefined;
};
type DefaultProps = {
  theme?: ThemeProps;
  navigation?: NavigationProp<RootFitnessStackParamList>;
};

type RootFitnessStackParamList = {
  Home: DefaultProps | { tokenRefresher: 'tokenRefresh' | undifined };
  Capture: DefaultProps & { onImageSelected: () => void };
  Coach: DefaultProps;
  Profile: DefaultProps;
  OnBoard: DefaultProps & { newUser?: boolean };
  Mealdata: {
    photoUri: string;
    fromTextAnalysis?: boolean;
    textDescription?: string;
    selectedMealType?: string;
    analysisResult?: DefaultProps | null;
  };
  Tabs: DefaultProps & { tokenRefresher?: string | 'tokenRefresh' };
  TextMealDetection: DefaultProps;
  TextManualMealDetection: DefaultProps;
  Settings: DefaultProps & { newUser?: boolean };
  CoachPreference: DefaultProps;
  SendFeedback: DefaultProps;
  CoachChatScreen: DefaultProps & { coachName?: string };
};

type GuestDefaultProps = {
  theme: ThemeProps;
  navigation: NavigationProp<RootGuestStackParamList>;
};
type RootGuestStackParamList = {
  Login: GuestDefaultProps;
  OnBoarding: GuestDefaultProps;
  Tabs: GuestDefaultProps & {
    tokenRefresher: 'tokenRefresh' | undifined;
    params?: RootFitnessStackParamList[keyof RootFitnessStackParamList];
  };
};

export type GuestScreenProps<RouteName extends keyof RootGuestStackParamList> =
  StackNavigationOptions<RootGuestStackParamList, RouteName>;

export type FitnessScreenProps<
  RouteName extends keyof RootFitnessStackParamList,
> = BottomTabScreenProps<RootFitnessStackParamList, RouteName>;

type ScreenProps<T extends keyof RootStackParamList> = {
  navigation: BottomTabNavigationProp<RootStackParamList, T>;
  route: RouteProp<RootStackParamList, T>;
};
