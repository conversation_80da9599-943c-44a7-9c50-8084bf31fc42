// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		00E356F31AD99517003FC87E /* DelicutFitnessAppTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* DelicutFitnessAppTests.m */; };
		05DAD193D2C0C2CE6DA98B71 /* libPods-DelicutFitnessApp-DelicutFitnessAppTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = B29B176712DAF7E2A5361DAB /* libPods-DelicutFitnessApp-DelicutFitnessAppTests.a */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		29CA6277FA5C48E741EF1147 /* libPods-DelicutFitnessApp.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 4744B0BD67E375375A43E194 /* libPods-DelicutFitnessApp.a */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		FF07420B2DEAB4DD00DA0809 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = FF07420A2DEAB4DD00DA0809 /* GoogleService-Info.plist */; };
		FFC1AE103272FB081FA7B08C /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */; };
		FFF2585C2D8C264F00F4F5BA /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = FFF2585B2D8C264F00F4F5BA /* GoogleService-Info.plist */; };
		FFF258662D8C266500F4F5BA /* Maison-Bold.otf in Resources */ = {isa = PBXBuildFile; fileRef = FFF2585D2D8C265C00F4F5BA /* Maison-Bold.otf */; };
		FFF258672D8C266500F4F5BA /* Maison-Demi.otf in Resources */ = {isa = PBXBuildFile; fileRef = FFF2585E2D8C265C00F4F5BA /* Maison-Demi.otf */; };
		FFF258682D8C266500F4F5BA /* Maison-Light.otf in Resources */ = {isa = PBXBuildFile; fileRef = FFF2585F2D8C265C00F4F5BA /* Maison-Light.otf */; };
		FFF258692D8C266500F4F5BA /* Maison-Medium.otf in Resources */ = {isa = PBXBuildFile; fileRef = FFF258602D8C265C00F4F5BA /* Maison-Medium.otf */; };
		FFF2586A2D8C266500F4F5BA /* Maison-Regular.otf in Resources */ = {isa = PBXBuildFile; fileRef = FFF258612D8C265C00F4F5BA /* Maison-Regular.otf */; };
		FFF2586B2D8C266500F4F5BA /* Maison-RegularOblique.otf in Resources */ = {isa = PBXBuildFile; fileRef = FFF258622D8C265C00F4F5BA /* Maison-RegularOblique.otf */; };
		FFF2586C2D8C266500F4F5BA /* MaisonMono-Bold.otf in Resources */ = {isa = PBXBuildFile; fileRef = FFF258632D8C265C00F4F5BA /* MaisonMono-Bold.otf */; };
		FFF2586D2D8C266500F4F5BA /* MaisonMono-Light.otf in Resources */ = {isa = PBXBuildFile; fileRef = FFF258642D8C265C00F4F5BA /* MaisonMono-Light.otf */; };
		FFF258802D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF2587B2D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF258812D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF2587F2D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF258822D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF258722D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF258832D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF2587A2D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF258842D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF258762D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF258852D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF258792D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF258862D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF2587E2D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF258872D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF258772D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF258882D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF258742D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF258892D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF2587D2D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF2588A2D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF258712D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF2588B2D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF258732D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF2588C2D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF258752D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF2588D2D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF258782D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF2588E2D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF2587C2D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF2588F2D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF2587B2D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF258902D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF2587F2D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF258912D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF258722D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF258922D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF2587A2D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF258932D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF258762D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF258942D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF258792D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF258952D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF2587E2D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF258962D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF258772D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF258972D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF258742D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF258982D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF2587D2D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF258992D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF258712D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF2589A2D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF258732D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF2589B2D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF258752D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF2589C2D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF258782D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF2589D2D8C2CC300F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF2587C2D8C2CC300F4F5BA /* <EMAIL> */; };
		FFF258A12D8C2CD600F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF258A02D8C2CD600F4F5BA /* <EMAIL> */; };
		FFF258A22D8C2CD600F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF2589E2D8C2CD600F4F5BA /* <EMAIL> */; };
		FFF258A32D8C2CD600F4F5BA /* iTunesArtwork@2x 2.png in Resources */ = {isa = PBXBuildFile; fileRef = FFF2589F2D8C2CD600F4F5BA /* iTunesArtwork@2x 2.png */; };
		FFF258A42D8C2CD600F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF258A02D8C2CD600F4F5BA /* <EMAIL> */; };
		FFF258A52D8C2CD600F4F5BA /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = FFF2589E2D8C2CD600F4F5BA /* <EMAIL> */; };
		FFF258A62D8C2CD600F4F5BA /* iTunesArtwork@2x 2.png in Resources */ = {isa = PBXBuildFile; fileRef = FFF2589F2D8C2CD600F4F5BA /* iTunesArtwork@2x 2.png */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = DelicutFitnessApp;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		00E356EE1AD99517003FC87E /* DelicutFitnessAppTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = DelicutFitnessAppTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* DelicutFitnessAppTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DelicutFitnessAppTests.m; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* DelicutFitnessApp.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = DelicutFitnessApp.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = DelicutFitnessApp/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = AppDelegate.mm; path = DelicutFitnessApp/AppDelegate.mm; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = DelicutFitnessApp/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = DelicutFitnessApp/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = DelicutFitnessApp/main.m; sourceTree = "<group>"; };
		13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = PrivacyInfo.xcprivacy; path = DelicutFitnessApp/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		4744B0BD67E375375A43E194 /* libPods-DelicutFitnessApp.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-DelicutFitnessApp.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		4EE5C394AFDC48153E1275B5 /* Pods-DelicutFitnessApp-DelicutFitnessAppTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-DelicutFitnessApp-DelicutFitnessAppTests.release.xcconfig"; path = "Target Support Files/Pods-DelicutFitnessApp-DelicutFitnessAppTests/Pods-DelicutFitnessApp-DelicutFitnessAppTests.release.xcconfig"; sourceTree = "<group>"; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = DelicutFitnessApp/LaunchScreen.storyboard; sourceTree = "<group>"; };
		A8ACAEB886B7419D2869DF65 /* Pods-DelicutFitnessApp-DelicutFitnessAppTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-DelicutFitnessApp-DelicutFitnessAppTests.debug.xcconfig"; path = "Target Support Files/Pods-DelicutFitnessApp-DelicutFitnessAppTests/Pods-DelicutFitnessApp-DelicutFitnessAppTests.debug.xcconfig"; sourceTree = "<group>"; };
		B29B176712DAF7E2A5361DAB /* libPods-DelicutFitnessApp-DelicutFitnessAppTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-DelicutFitnessApp-DelicutFitnessAppTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		B3759F439321EDAE884F0A42 /* Pods-DelicutFitnessApp.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-DelicutFitnessApp.debug.xcconfig"; path = "Target Support Files/Pods-DelicutFitnessApp/Pods-DelicutFitnessApp.debug.xcconfig"; sourceTree = "<group>"; };
		D898429470912514BE7C4C9A /* Pods-DelicutFitnessApp.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-DelicutFitnessApp.release.xcconfig"; path = "Target Support Files/Pods-DelicutFitnessApp/Pods-DelicutFitnessApp.release.xcconfig"; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		FF07420A2DEAB4DD00DA0809 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		FFCC347A2D8AEB4F00E2B14D /* DelicutFitnessApp.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = DelicutFitnessApp.entitlements; path = DelicutFitnessApp/DelicutFitnessApp.entitlements; sourceTree = "<group>"; };
		FFF2585B2D8C264F00F4F5BA /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		FFF2585D2D8C265C00F4F5BA /* Maison-Bold.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Maison-Bold.otf"; sourceTree = "<group>"; };
		FFF2585E2D8C265C00F4F5BA /* Maison-Demi.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Maison-Demi.otf"; sourceTree = "<group>"; };
		FFF2585F2D8C265C00F4F5BA /* Maison-Light.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Maison-Light.otf"; sourceTree = "<group>"; };
		FFF258602D8C265C00F4F5BA /* Maison-Medium.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Maison-Medium.otf"; sourceTree = "<group>"; };
		FFF258612D8C265C00F4F5BA /* Maison-Regular.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Maison-Regular.otf"; sourceTree = "<group>"; };
		FFF258622D8C265C00F4F5BA /* Maison-RegularOblique.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Maison-RegularOblique.otf"; sourceTree = "<group>"; };
		FFF258632D8C265C00F4F5BA /* MaisonMono-Bold.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "MaisonMono-Bold.otf"; sourceTree = "<group>"; };
		FFF258642D8C265C00F4F5BA /* MaisonMono-Light.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "MaisonMono-Light.otf"; sourceTree = "<group>"; };
		FFF258712D8C2CC300F4F5BA /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		FFF258722D8C2CC300F4F5BA /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		FFF258732D8C2CC300F4F5BA /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		FFF258742D8C2CC300F4F5BA /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		FFF258752D8C2CC300F4F5BA /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		FFF258762D8C2CC300F4F5BA /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		FFF258772D8C2CC300F4F5BA /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		FFF258782D8C2CC300F4F5BA /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		FFF258792D8C2CC300F4F5BA /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		FFF2587A2D8C2CC300F4F5BA /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		FFF2587B2D8C2CC300F4F5BA /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		FFF2587C2D8C2CC300F4F5BA /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		FFF2587D2D8C2CC300F4F5BA /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		FFF2587E2D8C2CC300F4F5BA /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		FFF2587F2D8C2CC300F4F5BA /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		FFF2589E2D8C2CD600F4F5BA /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		FFF2589F2D8C2CD600F4F5BA /* iTunesArtwork@2x 2.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "iTunesArtwork@2x 2.png"; sourceTree = "<group>"; };
		FFF258A02D8C2CD600F4F5BA /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				05DAD193D2C0C2CE6DA98B71 /* libPods-DelicutFitnessApp-DelicutFitnessAppTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				29CA6277FA5C48E741EF1147 /* libPods-DelicutFitnessApp.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* DelicutFitnessAppTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* DelicutFitnessAppTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = DelicutFitnessAppTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* DelicutFitnessApp */ = {
			isa = PBXGroup;
			children = (
				FFCC347A2D8AEB4F00E2B14D /* DelicutFitnessApp.entitlements */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.mm */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				13B07FB71A68108700A75B9A /* main.m */,
				13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */,
				FF07420A2DEAB4DD00DA0809 /* GoogleService-Info.plist */,
			);
			name = DelicutFitnessApp;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				4744B0BD67E375375A43E194 /* libPods-DelicutFitnessApp.a */,
				B29B176712DAF7E2A5361DAB /* libPods-DelicutFitnessApp-DelicutFitnessAppTests.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				FFF258702D8C2C7C00F4F5BA /* AppIcon */,
				FFF258652D8C265C00F4F5BA /* fonts */,
				FFF2585B2D8C264F00F4F5BA /* GoogleService-Info.plist */,
				13B07FAE1A68108700A75B9A /* DelicutFitnessApp */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* DelicutFitnessAppTests */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* DelicutFitnessApp.app */,
				00E356EE1AD99517003FC87E /* DelicutFitnessAppTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				B3759F439321EDAE884F0A42 /* Pods-DelicutFitnessApp.debug.xcconfig */,
				D898429470912514BE7C4C9A /* Pods-DelicutFitnessApp.release.xcconfig */,
				A8ACAEB886B7419D2869DF65 /* Pods-DelicutFitnessApp-DelicutFitnessAppTests.debug.xcconfig */,
				4EE5C394AFDC48153E1275B5 /* Pods-DelicutFitnessApp-DelicutFitnessAppTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		FFF258652D8C265C00F4F5BA /* fonts */ = {
			isa = PBXGroup;
			children = (
				FFF2585D2D8C265C00F4F5BA /* Maison-Bold.otf */,
				FFF2585E2D8C265C00F4F5BA /* Maison-Demi.otf */,
				FFF2585F2D8C265C00F4F5BA /* Maison-Light.otf */,
				FFF258602D8C265C00F4F5BA /* Maison-Medium.otf */,
				FFF258612D8C265C00F4F5BA /* Maison-Regular.otf */,
				FFF258622D8C265C00F4F5BA /* Maison-RegularOblique.otf */,
				FFF258632D8C265C00F4F5BA /* MaisonMono-Bold.otf */,
				FFF258642D8C265C00F4F5BA /* MaisonMono-Light.otf */,
			);
			path = fonts;
			sourceTree = "<group>";
		};
		FFF258702D8C2C7C00F4F5BA /* AppIcon */ = {
			isa = PBXGroup;
			children = (
				FFF2589E2D8C2CD600F4F5BA /* <EMAIL> */,
				FFF2589F2D8C2CD600F4F5BA /* iTunesArtwork@2x 2.png */,
				FFF258A02D8C2CD600F4F5BA /* <EMAIL> */,
				FFF258712D8C2CC300F4F5BA /* <EMAIL> */,
				FFF258722D8C2CC300F4F5BA /* <EMAIL> */,
				FFF258732D8C2CC300F4F5BA /* <EMAIL> */,
				FFF258742D8C2CC300F4F5BA /* <EMAIL> */,
				FFF258752D8C2CC300F4F5BA /* <EMAIL> */,
				FFF258762D8C2CC300F4F5BA /* <EMAIL> */,
				FFF258772D8C2CC300F4F5BA /* <EMAIL> */,
				FFF258782D8C2CC300F4F5BA /* <EMAIL> */,
				FFF258792D8C2CC300F4F5BA /* <EMAIL> */,
				FFF2587A2D8C2CC300F4F5BA /* <EMAIL> */,
				FFF2587B2D8C2CC300F4F5BA /* <EMAIL> */,
				FFF2587C2D8C2CC300F4F5BA /* <EMAIL> */,
				FFF2587D2D8C2CC300F4F5BA /* <EMAIL> */,
				FFF2587E2D8C2CC300F4F5BA /* <EMAIL> */,
				FFF2587F2D8C2CC300F4F5BA /* <EMAIL> */,
			);
			path = AppIcon;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* DelicutFitnessAppTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "DelicutFitnessAppTests" */;
			buildPhases = (
				09E8F7B2573A1174FE01C1DE /* [CP] Check Pods Manifest.lock */,
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
				43CA9C6BC32C58B250A23ADB /* [CP] Embed Pods Frameworks */,
				4A0D73DC1CCE06A04BC9856F /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = DelicutFitnessAppTests;
			productName = DelicutFitnessAppTests;
			productReference = 00E356EE1AD99517003FC87E /* DelicutFitnessAppTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		13B07F861A680F5B00A75B9A /* DelicutFitnessApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "DelicutFitnessApp" */;
			buildPhases = (
				7C28C1C847447CF114E4F1E1 /* [CP] Check Pods Manifest.lock */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				4E9266476132C7F826F2DF9A /* [CP] Embed Pods Frameworks */,
				EB3FA089E755BF0163122D9D /* [CP] Copy Pods Resources */,
				C1C64E36CD728A26F858BA1E /* [CP-User] [RNFB] Core Configuration */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = DelicutFitnessApp;
			productName = DelicutFitnessApp;
			productReference = 13B07F961A680F5B00A75B9A /* DelicutFitnessApp.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "DelicutFitnessApp" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* DelicutFitnessApp */,
				00E356ED1AD99517003FC87E /* DelicutFitnessAppTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				FFF258802D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF258812D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF258822D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF258832D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF258842D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF258852D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF258862D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF258872D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF258882D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF258892D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF2588A2D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF2588B2D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF2588C2D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF258A42D8C2CD600F4F5BA /* <EMAIL> in Resources */,
				FFF258A52D8C2CD600F4F5BA /* <EMAIL> in Resources */,
				FFF258A62D8C2CD600F4F5BA /* iTunesArtwork@2x 2.png in Resources */,
				FFF2588D2D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF2588E2D8C2CC300F4F5BA /* <EMAIL> in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				FFF258662D8C266500F4F5BA /* Maison-Bold.otf in Resources */,
				FFF258672D8C266500F4F5BA /* Maison-Demi.otf in Resources */,
				FFF258682D8C266500F4F5BA /* Maison-Light.otf in Resources */,
				FFF258692D8C266500F4F5BA /* Maison-Medium.otf in Resources */,
				FFF2586A2D8C266500F4F5BA /* Maison-Regular.otf in Resources */,
				FFF2586B2D8C266500F4F5BA /* Maison-RegularOblique.otf in Resources */,
				FFF2586C2D8C266500F4F5BA /* MaisonMono-Bold.otf in Resources */,
				FFF2586D2D8C266500F4F5BA /* MaisonMono-Light.otf in Resources */,
				FFF2585C2D8C264F00F4F5BA /* GoogleService-Info.plist in Resources */,
				FFF258A12D8C2CD600F4F5BA /* <EMAIL> in Resources */,
				FFF258A22D8C2CD600F4F5BA /* <EMAIL> in Resources */,
				FFF258A32D8C2CD600F4F5BA /* iTunesArtwork@2x 2.png in Resources */,
				FFF2588F2D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF258902D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF258912D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FF07420B2DEAB4DD00DA0809 /* GoogleService-Info.plist in Resources */,
				FFF258922D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF258932D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF258942D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF258952D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF258962D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF258972D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF258982D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF258992D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF2589A2D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF2589B2D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF2589C2D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				FFF2589D2D8C2CC300F4F5BA /* <EMAIL> in Resources */,
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				FFC1AE103272FB081FA7B08C /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nREACT_NATIVE_PATH=\"${PODS_ROOT}/../../node_modules/react-native\"\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\necho \"✅ Starting React Native bundle\"\necho \"📍 REACT_NATIVE_PATH = $REACT_NATIVE_PATH\"\necho \"🛠 Executing: $WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		09E8F7B2573A1174FE01C1DE /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-DelicutFitnessApp-DelicutFitnessAppTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		43CA9C6BC32C58B250A23ADB /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-DelicutFitnessApp-DelicutFitnessAppTests/Pods-DelicutFitnessApp-DelicutFitnessAppTests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-DelicutFitnessApp-DelicutFitnessAppTests/Pods-DelicutFitnessApp-DelicutFitnessAppTests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-DelicutFitnessApp-DelicutFitnessAppTests/Pods-DelicutFitnessApp-DelicutFitnessAppTests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		4A0D73DC1CCE06A04BC9856F /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-DelicutFitnessApp-DelicutFitnessAppTests/Pods-DelicutFitnessApp-DelicutFitnessAppTests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-DelicutFitnessApp-DelicutFitnessAppTests/Pods-DelicutFitnessApp-DelicutFitnessAppTests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-DelicutFitnessApp-DelicutFitnessAppTests/Pods-DelicutFitnessApp-DelicutFitnessAppTests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		4E9266476132C7F826F2DF9A /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-DelicutFitnessApp/Pods-DelicutFitnessApp-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-DelicutFitnessApp/Pods-DelicutFitnessApp-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-DelicutFitnessApp/Pods-DelicutFitnessApp-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		7C28C1C847447CF114E4F1E1 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-DelicutFitnessApp-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		C1C64E36CD728A26F858BA1E /* [CP-User] [RNFB] Core Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Core Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\n\n##########################################################################\n##########################################################################\n#\n#  NOTE THAT IF YOU CHANGE THIS FILE YOU MUST RUN pod install AFTERWARDS\n#\n#  This file is installed as an Xcode build script in the project file\n#  by cocoapods, and you will not see your changes until you pod install\n#\n##########################################################################\n##########################################################################\n\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_JSON_ROOT=\"'react-native'\"\n_JSON_FILE_NAME='firebase.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getFirebaseJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> RNFB build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    if ! python3 --version >/dev/null 2>&1; then echo \"python3 not found, firebase.json file processing error.\" && exit 1; fi\n    _JSON_OUTPUT_BASE64=$(python3 -c 'import json,sys,base64;print(base64.b64encode(bytes(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"', '\"'rb'\"').read())['${_JSON_ROOT}']), '\"'utf-8'\"')).decode())' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.app_data_collection_default_enabled\n  _APP_DATA_COLLECTION_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_data_collection_default_enabled\")\n  if [[ $_APP_DATA_COLLECTION_ENABLED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseDataCollectionDefaultEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_DATA_COLLECTION_ENABLED\")\")\n  fi\n\n  # config.analytics_auto_collection_enabled\n  _ANALYTICS_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_auto_collection_enabled\")\n  if [[ $_ANALYTICS_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_COLLECTION\")\")\n  fi\n\n  # config.analytics_collection_deactivated\n  _ANALYTICS_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_collection_deactivated\")\n  if [[ $_ANALYTICS_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_DEACTIVATED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_DEACTIVATED\")\")\n  fi\n\n  # config.analytics_idfv_collection_enabled\n  _ANALYTICS_IDFV_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_idfv_collection_enabled\")\n  if [[ $_ANALYTICS_IDFV_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_IDFV_COLLECTION\")\")\n  fi\n\n  # config.analytics_default_allow_analytics_storage\n  _ANALYTICS_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_analytics_storage\")\n  if [[ $_ANALYTICS_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_ANALYTICS_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_storage\n  _ANALYTICS_AD_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_storage\")\n  if [[ $_ANALYTICS_AD_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_user_data\n  _ANALYTICS_AD_USER_DATA=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_user_data\")\n  if [[ $_ANALYTICS_AD_USER_DATA ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_USER_DATA\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_USER_DATA\")\")\n  fi\n\n  # config.analytics_default_allow_ad_personalization_signals\n  _ANALYTICS_PERSONALIZATION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_personalization_signals\")\n  if [[ $_ANALYTICS_PERSONALIZATION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_PERSONALIZATION\")\")\n  fi\n\n  # config.analytics_registration_with_ad_network_enabled\n  _ANALYTICS_REGISTRATION_WITH_AD_NETWORK=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_registration_with_ad_network_enabled\")\n  if [[ $_ANALYTICS_REGISTRATION_WITH_AD_NETWORK ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_REGISTRATION_WITH_AD_NETWORK_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_REGISTRATION_WITH_AD_NETWORK\")\")\n  fi\n\n  # config.google_analytics_automatic_screen_reporting_enabled\n  _ANALYTICS_AUTO_SCREEN_REPORTING=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_automatic_screen_reporting_enabled\")\n  if [[ $_ANALYTICS_AUTO_SCREEN_REPORTING ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAutomaticScreenReportingEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_SCREEN_REPORTING\")\")\n  fi\n\n  # config.perf_auto_collection_enabled\n  _PERF_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_auto_collection_enabled\")\n  if [[ $_PERF_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_enabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_AUTO_COLLECTION\")\")\n  fi\n\n  # config.perf_collection_deactivated\n  _PERF_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_collection_deactivated\")\n  if [[ $_PERF_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_deactivated\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_DEACTIVATED\")\")\n  fi\n\n  # config.messaging_auto_init_enabled\n  _MESSAGING_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"messaging_auto_init_enabled\")\n  if [[ $_MESSAGING_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseMessagingAutoInitEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_MESSAGING_AUTO_INIT\")\")\n  fi\n\n  # config.in_app_messaging_auto_colllection_enabled\n  _FIAM_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"in_app_messaging_auto_collection_enabled\")\n  if [[ $_FIAM_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseInAppMessagingAutomaticDataCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_FIAM_AUTO_INIT\")\")\n  fi\n\n  # config.app_check_token_auto_refresh\n  _APP_CHECK_TOKEN_AUTO_REFRESH=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_check_token_auto_refresh\")\n  if [[ $_APP_CHECK_TOKEN_AUTO_REFRESH ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAppCheckTokenAutoRefreshEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_CHECK_TOKEN_AUTO_REFRESH\")\")\n  fi\n\n  # config.crashlytics_disable_auto_disabler - undocumented for now - mainly for debugging, document if becomes useful\n  _CRASHLYTICS_AUTO_DISABLE_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"crashlytics_disable_auto_disabler\")\n  if [[ $_CRASHLYTICS_AUTO_DISABLE_ENABLED == \"true\" ]]; then\n    echo \"Disabled Crashlytics auto disabler.\" # do nothing\n  else\n    _PLIST_ENTRY_KEYS+=(\"FirebaseCrashlyticsCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"NO\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A firebase.json file was not found, whilst this file is optional it is recommended to include it to configure firebase services in React Native Firebase.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- RNFB build script finished\"\n";
		};
		EB3FA089E755BF0163122D9D /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-DelicutFitnessApp/Pods-DelicutFitnessApp-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-DelicutFitnessApp/Pods-DelicutFitnessApp-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-DelicutFitnessApp/Pods-DelicutFitnessApp-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				00E356F31AD99517003FC87E /* DelicutFitnessAppTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* DelicutFitnessApp */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		00E356F61AD99517003FC87E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A8ACAEB886B7419D2869DF65 /* Pods-DelicutFitnessApp-DelicutFitnessAppTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = DelicutFitnessAppTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/DelicutFitnessApp.app/DelicutFitnessApp";
			};
			name = Debug;
		};
		00E356F71AD99517003FC87E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4EE5C394AFDC48153E1275B5 /* Pods-DelicutFitnessApp-DelicutFitnessAppTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				INFOPLIST_FILE = DelicutFitnessAppTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/DelicutFitnessApp.app/DelicutFitnessApp";
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B3759F439321EDAE884F0A42 /* Pods-DelicutFitnessApp.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = DelicutFitnessApp/DelicutFitnessApp.entitlements;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 78WCYT7LA2;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = DelicutFitnessApp/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Delicut;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.delicut.fitness;
				PRODUCT_NAME = DelicutFitnessApp;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D898429470912514BE7C4C9A /* Pods-DelicutFitnessApp.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = DelicutFitnessApp/DelicutFitnessApp.entitlements;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 78WCYT7LA2;
				INFOPLIST_FILE = DelicutFitnessApp/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Delicut;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.delicut.fitness;
				PRODUCT_NAME = DelicutFitnessApp;
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) DEBUG";
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "DelicutFitnessAppTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00E356F61AD99517003FC87E /* Debug */,
				00E356F71AD99517003FC87E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "DelicutFitnessApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "DelicutFitnessApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
