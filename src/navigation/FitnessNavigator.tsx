import messaging from '@react-native-firebase/messaging';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import {
  createStackNavigator,
  StackNavigationProp,
  StackScreenProps,
} from '@react-navigation/stack';
import React, { useCallback, useEffect } from 'react';
import { Alert, PermissionsAndroid, Platform } from 'react-native';

import { RootFitnessStackParamList } from '../../@types/navigation';
import FitnessTabBar from '../components/TabNavigator/FitnessTabBar';

import { RouteProp, useNavigation } from '@react-navigation/native';
import PushNotification from 'react-native-push-notification';
import { useDispatch } from 'react-redux';
import FitnessHeader from '../components/Header/fitnessHeader';
import { storeNotificationMessage } from '../redux/slices/appSlice';
import CoachChatScreen from '../screens/fitness/CoachChatScreen';
import MainScreen from '../screens/fitness/mainscreen';
import Mealdata from '../screens/fitness/mealdata';
import Profile from '../screens/fitness/profile';
import CoachPreferenceScreen from '../screens/Users/<USER>/CoachPreferenceScreen';
import SendFeedbackScreen from '../screens/Users/<USER>/SendFeedbackScreen';
import SettingScreen from '../screens/Users/<USER>/SettingScreen';
import { LightTheme } from '../theme/colors';

import CameraScreen from '../screens/fitness/camera';
import OnBoard from '../screens/fitness/onboard';
import TextManualMealDetection from '../screens/fitness/textmanualmealdetection';
import TextMealDetection from '../screens/fitness/textmealdetection';

const Stack = createStackNavigator<RootFitnessStackParamList>();
const Tab = createBottomTabNavigator<RootFitnessStackParamList>();
const StackNavigator: React.FC<
  StackScreenProps<RootFitnessStackParamList, 'Tabs'>
> = () => {
  const theme = LightTheme;
  const navigation = useNavigation();

  return (
    <Stack.Navigator
      initialRouteName="Home"
      screenOptions={{
        headerShown: false,
        header: ({ navigation, route }) => (
          <FitnessHeader
            navigation={
              navigation as StackNavigationProp<RootFitnessStackParamList>
            }
            route={route as RouteProp<RootFitnessStackParamList>}
          />
        ),
      }}
    >
      <Stack.Screen
        name="Home"
        component={MainScreen}
        options={{
          headerShown: true,
        }}
        initialParams={{ theme, navigation }}
      />
      <Stack.Screen
        name="Settings"
        component={SettingScreen}
        initialParams={{ theme, navigation }}
      />
      <Stack.Screen
        name="Coach"
        component={CoachChatScreen}
        initialParams={{ theme, navigation }}
      />
      <Stack.Screen
        name="Profile"
        component={Profile}
        initialParams={{ theme, navigation }}
      />
      <Stack.Screen
        name="OnBoard"
        component={OnBoard}
        initialParams={{ theme, navigation }}
      />
      <Stack.Screen
        name="Capture"
        component={CameraScreen}
        initialParams={{ theme, navigation }}
      />
      <Stack.Screen
        name="Mealdata"
        component={Mealdata}
        initialParams={{ theme, navigation }}
      />
      <Stack.Screen
        name="TextMealDetection"
        component={TextMealDetection}
        initialParams={{ theme, navigation }}
      />
      <Stack.Screen
        name="TextManualMealDetection"
        component={TextManualMealDetection}
        initialParams={{ theme, navigation }}
      />
      <Stack.Screen
        name="CoachPreference"
        component={CoachPreferenceScreen}
        initialParams={{ theme, navigation }}
      />
      <Stack.Screen
        name="SendFeedback"
        component={SendFeedbackScreen}
        initialParams={{ theme, navigation }}
      />
    </Stack.Navigator>
  );
};

export default function FitnessNavigator() {
  const dispatch = useDispatch();
  const navigation = useNavigation();

  // Configure PushNotification
  useEffect(() => {
    PushNotification.configure({
      onNotification: function (notification) {
        console.log('Local notification clicked:', notification);

        // When user clicks on notification, clear it from app and navigate to home
        if (notification.userInteraction) {
          // Clear the notification from Redux store
          dispatch(storeNotificationMessage(null));

          // Navigate to Home screen
          try {
            // @ts-expect-error - navigation type issue, but this works
            navigation.navigate('Tabs', { screen: 'Home' });
          } catch (error) {
            console.warn('Navigation error from notification click:', error);
          }
        }
      },
      requestPermissions: Platform.OS === 'ios',
    });
  }, [dispatch, navigation]);

  const handleNotificationClick = useCallback(
    (
      remoteMessage: { notification?: { title?: string; body?: string } },
      source: string,
    ) => {
      console.log(`Notification clicked (${source}):`, { remoteMessage });

      if (remoteMessage?.notification) {
        const title = remoteMessage.notification.title ?? 'Notification';
        const body = remoteMessage.notification.body ?? '';

        // Store the LATEST notification in Redux (replaces any existing one)
        dispatch(
          storeNotificationMessage({
            title: title,
            body: body,
          }),
        );

        // Navigate to Home screen to show the notification
        try {
          // @ts-expect-error - navigation type issue, but this works
          navigation.navigate('Tabs', { screen: 'Home' });
        } catch (error) {
          console.warn('Navigation error after notification click:', error);
        }
      }
    },
    [dispatch, navigation],
  );

  useEffect(() => {
    const requestNotificationPermission = async () => {
      if (Platform.OS === 'android' && Platform.Version >= 33) {
        try {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
          );
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            console.log('Notification permission granted on Android 13+');
          } else {
            Alert.alert(
              'Permission required',
              'Enable notifications in your settings to receive updates.',
            );
          }
        } catch (error) {
          console.warn('Notification permission request error:', error);
        }
      }
    };

    requestNotificationPermission();

    // Handle foreground notifications (when app is open)
    const unsubscribeForeground = messaging().onMessage(
      async (remoteMessage) => {
        console.log('Foreground notification received:', { remoteMessage });

        if (remoteMessage?.notification) {
          const title = remoteMessage.notification.title ?? 'Notification';
          const body = remoteMessage.notification.body ?? '';

          // 1. Show local notification in notification bar
          PushNotification.localNotification({
            title: title,
            message: body,
            playSound: true,
            soundName: 'default',
            vibrate: true,
            vibration: 300,
            importance: 'high',
            priority: 'high',
            id: Date.now().toString(), // Unique ID for each notification
          });

          // 2. Store the LATEST notification in Redux (replaces any existing one)
          dispatch(
            storeNotificationMessage({
              title: title,
              body: body,
            }),
          );

          // 3. Navigate to Home screen to ensure notification is visible
          // try {
          //   // @ts-expect-error - navigation type issue, but this works
          //   // navigation.navigate('Tabs', { screen: 'Home' });
          //   console.log({
          //     _______________________________________:
          //       '_______________________________________',
          //   });
          // } catch (error) {
          //   console.warn(
          //     'Navigation error for foreground notification:',
          //     error,
          //   );
          // }
        }
      },
    );

    // Handle notification clicks when app is in background
    const unsubscribeBackground = messaging().onNotificationOpenedApp(
      async (remoteMessage) => {
        handleNotificationClick(remoteMessage, 'app in background');
      },
    );

    // Handle notification clicks when app is completely closed
    messaging()
      .getInitialNotification()
      .then(async (remoteMessage) => {
        if (remoteMessage) {
          handleNotificationClick(remoteMessage, 'app was closed');
        }
      });

    return () => {
      unsubscribeForeground();
      unsubscribeBackground();
    };
  }, [dispatch, navigation, handleNotificationClick]);

  return (
    <Tab.Navigator
      tabBar={(props) => <FitnessTabBar {...props} />}
      initialRouteName="Tabs"
      screenOptions={{
        headerShown: false,
        tabBarHideOnKeyboard: true,
      }}
    >
      <Tab.Screen name="Tabs" component={StackNavigator} />
    </Tab.Navigator>
  );
}
