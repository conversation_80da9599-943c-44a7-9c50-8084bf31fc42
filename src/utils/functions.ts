/* eslint-disable @typescript-eslint/no-explicit-any */
import AsyncStorage from '@react-native-async-storage/async-storage';
import { WeekDeliveryItemProps } from 'delivery-slice';
import moment from 'moment-timezone';
import { useEffect } from 'react';
import { UseFormSetError } from 'react-hook-form';
import { BackHandler, Linking } from 'react-native';
import Toast from 'react-native-toast-message';
import { colors } from 'theme-actions';
import { KcalRangeProps, PortionMacroProps } from '../../@types/meal-plan';
import { AlphaNumericRegex } from './regexMatch';

export const storeDataToAsyncStorage = async (key: string, value: string) => {
  try {
    await AsyncStorage.setItem(key, value);
  } catch (e) {
    console.error('storeDataToAsyncStorage error', e);
  }
};

export const getDataFromAsyncStorage = async (key: string) => {
  try {
    const jsonValue = await AsyncStorage.getItem(key);
    return jsonValue != null ? jsonValue : null;
  } catch (e) {
    console.error('getDataFromAsyncStorage error', e);
  }
};

export const removeItemFromAsyncStorage = async (key: string) => {
  try {
    await AsyncStorage.removeItem(key);
  } catch (e) {
    console.error('removeItemFromAsyncStorage error', e);
  }
};
export function getWeekCategory(inputDate: string) {
  const timeZone = 'Asia/Dubai';
  const today = moment.tz(timeZone);
  const startOfWeek = today.clone().startOf('week'); // Sunday
  const endOfWeek = today.clone().endOf('week'); // Saturday
  const startOfPastWeek = startOfWeek.clone().subtract(1, 'week');
  const endOfPastWeek = endOfWeek.clone().subtract(1, 'week');
  const startOfNextWeek = startOfWeek.clone().add(1, 'week');
  const endOfNextWeek = endOfWeek.clone().add(1, 'week');
  const dateToCheck = moment.tz(inputDate, timeZone);
  if (dateToCheck.isBetween(startOfWeek, endOfWeek, null, '[]')) {
    return 'current_week';
  } else if (
    dateToCheck.isBetween(startOfNextWeek, endOfNextWeek, null, '[]')
  ) {
    return 'next_week';
  } else if (
    dateToCheck.isBetween(startOfPastWeek, endOfPastWeek, null, '[]')
  ) {
    return 'past_week';
  } else {
    return 'not_in_range';
  }
}
export function TitleCase(string: string) {
  const str = string?.replaceAll('_', ' ');
  if (str) return str[0]?.toUpperCase() + str?.slice(1).toLowerCase();
  return '';
}
export function removeDuplicates(arr: Array<string>) {
  return arr?.filter((item, index) => arr.indexOf(item) === index);
}
export const maskEmail = (email: string) => {
  const [name, domain] = email.split('@');
  if (!name || !domain) return email;

  const maskedName =
    name.length > 2
      ? name.substring(0, 2) + '*'.repeat(3) + name.slice(-3)
      : name.charAt(0) + '*'.repeat(3);

  return `${maskedName}@${domain}`;
};

export const maskPhoneNumber = (phone: string) => {
  if (phone.length < 9) return phone; // Handle edge case
  return `+971 ${phone.substring(0, 2)}*****${phone.slice(-2)}`;
};
export const formatSelectedNames = (names: string[], limit: number = 3) => {
  if (names?.length <= limit) {
    return names?.join(', ');
  }
  const visibleNames = names?.slice(0, limit).join(', ');
  const remainingCount = names?.length - limit;
  return `${visibleNames}.. +${remainingCount} more`;
};

export function updateSelection(
  allOptions: string[],
  selectedItems: any[],
  newItem: string,
) {
  let updatedSelection = [...selectedItems];
  if (newItem === 'all_meal') {
    if (selectedItems.includes(newItem)) {
      updatedSelection = []; // Deselect everything if "all_meal" is selected again
    } else {
      updatedSelection = [newItem]; // Select only "all_meal"
    }
  } else if (newItem === '2_snack') {
    if (selectedItems.includes(newItem)) {
      updatedSelection = selectedItems.filter((e: any) => e !== newItem);
    } else {
      updatedSelection = [
        ...selectedItems.filter(
          (e: string) => e !== '1_snack' && e !== 'all_meal',
        ),
        newItem,
      ];
    }
  } else if (newItem === '1_snack') {
    if (selectedItems.includes(newItem)) {
      updatedSelection = selectedItems.filter((e: any) => e !== newItem);
    } else {
      updatedSelection = [
        ...selectedItems.filter(
          (e: string) => e !== '2_snack' && e !== 'all_meal',
        ),
        newItem,
      ];
    }
  } else {
    if (selectedItems.includes(newItem)) {
      updatedSelection = selectedItems.filter((e: any) => e !== newItem);
    } else {
      updatedSelection = [
        ...selectedItems.filter((e: string) => e !== 'all_meal'),
        newItem,
      ];
    }
  }
  const withoutAllMeal = allOptions.filter((option: string) =>
    allOptions?.includes('2_snack')
      ? option !== '1_snack' && option !== 'all_meal'
      : option,
  );
  const allWithoutAllMealSelected = withoutAllMeal.every((option: any) =>
    updatedSelection.includes(option),
  );
  if (
    allWithoutAllMealSelected &&
    !updatedSelection.includes('all_meal') &&
    allOptions.includes('all_meal')
  ) {
    updatedSelection = ['all_meal'];
  }

  return updatedSelection;
}
export function areAllElementsPresent(arr1: string[], arr2: string[]) {
  for (let i = 0; i < arr2.length; i++) {
    if (!arr1.includes(arr2[i])) {
      return false;
    }
  }
  return true;
}
export function mergeArrays(arr1: string[], arr2: string[]) {
  const mergedArray = arr1.concat(arr2);
  const uniqueArray = [...new Set(mergedArray)];
  return uniqueArray;
}
export const checker = (arr: string[], target: string[]) => {
  if (target.length == 0) {
    return false;
  } else {
    return target.every((v) => arr.includes(v));
  }
};
export function showSuccessToast(message: string) {
  Toast.show({
    type: 'success',
    text1: message,
    autoHide: true,
    position: 'bottom',
  });
}
export function showErrorToast(message: string) {
  Toast.show({
    type: 'error',
    text1: message,
    autoHide: true,
    position: 'bottom',
  });
}

export function validateEnglishAlphanumeric(value: string) {
  const validPattern = AlphaNumericRegex;
  if (!validPattern.test(value)) {
    showErrorToast('Only English letters and numbers are allowed.');
    return false;
  }
  return true;
}

export function validInputPattern(
  value: string,
  fieldName?: string,
  errorMessage?: string,
  setError?: UseFormSetError<any>,
) {
  const validPattern = AlphaNumericRegex;
  if (!validPattern.test(value)) {
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    setError && setError(fieldName as string, { message: errorMessage });
    return false;
  }
  return true;
}

export const formatPrice = (
  amount: number | string,
  locale: string = 'en-US',
) => {
  return new Intl.NumberFormat(locale, {
    style: 'decimal',
    minimumFractionDigits: 2,
  }).format(typeof amount === 'number' ? amount : parseFloat(amount));
};
export const openWhatsapp = async () => {
  if (await Linking.canOpenURL(`whatsapp://send?phone=${'971565225220'}`)) {
    Linking.openURL(`whatsapp://send?phone=${'971565225220'}`);
  } else {
    Linking.openURL('https://wa.me/971565225220');
  }
};
export const formatDateAsEpoch = (date: string | Date) => {
  let newd = new Date(date);
  const epochInSeconds = Math.floor(newd.getTime() / 1000);
  return `$D_${epochInSeconds}`;
};

export const getmacrosCardData = (title: string, colors: colors): string => {
  if (title === 'PROTEIN') {
    return colors?.primary_grenade;
  } else if (title === 'CARB') {
    return colors?.secondary_curcuma;
  } else if (title === 'FAT') {
    return colors?.secondary_avocado;
  }
  return '';
};

export function formatDateInDubai(dateInput: string) {
  const dubaiTz = 'Asia/Dubai';
  const date = moment.tz(dateInput, dubaiTz).startOf('day');
  const today = moment.tz(dubaiTz).startOf('day');
  const yesterday = moment(today).subtract(1, 'day');
  const tomorrow = moment(today).add(1, 'day');

  if (date.isSame(today)) {
    return 'Today';
  } else if (date.isSame(yesterday)) {
    return 'Yesterday';
  } else if (date.isSame(tomorrow)) {
    return 'Tomorrow';
  } else {
    return date.format('ddd, D MMM'); // Ex: Mon, 5 May
  }
}
export function getNearestFutureDate(datesArray: string[]) {
  const refTime = new Date();
  const futureDates = datesArray
    .map((d) => new Date(d))
    .filter((d) => d.getTime() > refTime.getTime());

  if (futureDates.length === 0) {
    return null; // No future date found
  }

  // Find the date with the smallest difference from the reference date
  let nearestDate = futureDates[0];
  let minDiff = nearestDate.getTime() - refTime.getTime();

  for (let i = 1; i < futureDates.length; i++) {
    const diff = futureDates[i].getTime() - refTime.getTime();
    if (diff < minDiff) {
      minDiff = diff;
      nearestDate = futureDates[i];
    }
  }

  return nearestDate;
}

export const arraysAreEqual = <T>(arr1: T[], arr2: T[]): boolean => {
  if (arr1.length !== arr2.length) return false;

  const sortedArr1 = [...arr1].sort();
  const sortedArr2 = [...arr2].sort();

  return sortedArr1.every((val, index) => val === sortedArr2[index]);
};
export function sumVariantsField(
  deliveryByDate: WeekDeliveryItemProps | undefined,
  field: keyof NonNullable<
    WeekDeliveryItemProps['delivery_item']
  >[number]['selected_meal']['variants'],
): number {
  if (!deliveryByDate?.delivery_item) return 0;

  return deliveryByDate.delivery_item
    .filter((item) => item.selected_meal?.is_taken)
    .reduce((sum, item) => {
      const value = item.selected_meal?.variants?.[field];
      return sum + (typeof value === 'number' ? value : 0);
    }, 0);
}
export function daysPending(targetDate: Date | string): number {
  const today = new Date();
  const date = new Date(targetDate);

  // Calculate difference in milliseconds
  const diffTime = date.getTime() - today.getTime();

  // Convert milliseconds to days
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  // If date is in the past, return 0 (or negative number if you prefer)
  return diffDays > 0 ? diffDays : 0;
}

export function getMacroAverage(
  portioning: PortionMacroProps[] | undefined,
  portion: string,
  color: string,
): number {
  const macro: KcalRangeProps | undefined = portioning
    ?.filter((e) => e.portion === portion)
    .flatMap((e) => e.macro)
    .find((macro) => macro.color === color);

  if (!macro) return 1;

  const { start, end } = macro.range;
  return (start + end) / 2;
}

export const useBackHandlerWithCallback = (callback: () => boolean) => {
  useEffect(() => {
    const onBackPress = () => {
      if (callback) callback();
      return true; // Block default back action
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      onBackPress,
    );

    return () => backHandler.remove();
  }, [callback]);
};
export function capitalizeFirstLetter(str: string) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}
