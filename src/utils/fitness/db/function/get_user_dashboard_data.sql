CREATE OR REPLACE FUNCTION public.get_user_dashboard_data(p_user_id uuid, p_start_time timestamp with time zone, p_end_time timestamp with time zone)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$declare
  v_steps integer := 0;
  v_calories_burned numeric := 0;
  v_calories_consumed numeric := 0;
  v_target_steps integer := 0;
  v_target_calories numeric := 0;
  v_target_intake_calories numeric := 0;
  v_last_synced timestamp;
  v_activities jsonb;
  v_user_name text;

  -- Basal Metabolic Burn vars
  v_weight numeric;
  v_height numeric;
  v_birthdate date;
  v_gender text;
  v_age int;
  v_bmr numeric := 0;
  v_bmr_hourly numeric := 0;
  v_hours_today numeric := 0;
  v_basal_burn numeric := 0;
  v_target_calorie_deficit numeric := 0; -- Added target_calorie_deficit
  v_user_timezone text;
begin
  -- Get user full name
  select full_name
  into v_user_name
  from users
  where id = p_user_id;

  -- Total steps
  select coalesce(sum(count), 0)
  into v_steps
  from steps
  where user_id = p_user_id
    and start_time >= p_start_time
    and end_time <= p_end_time;



  -- Calories consumed from meals
  select round(coalesce(sum(total_calories), 0)::numeric)
  into v_calories_consumed
  from meal_logs
  where user_id = p_user_id
    and created_at >= p_start_time
    and created_at <= p_end_time;

  -- User goals and body data
  select 
    coalesce(daily_goals_steps, 0),
    round(coalesce(target_calorie, 0)::numeric),
    round(coalesce(target_intake_calorie, 0)::numeric),
    height,
    date_of_birth,
    lower(gender),
    round(coalesce(target_calorie_deficit, 0)::numeric), -- Added target_calorie_deficit
    timezone
  into 
    v_target_steps,
    v_target_calories,
    v_target_intake_calories,
    v_height,
    v_birthdate,
    v_gender,
    v_target_calorie_deficit,
    v_user_timezone
  from user_fitness_profiles
  where user_id = p_user_id;

-- User weight (latest entry)
select weight_kg
into v_weight
from (
  select weight_kg
  from user_weight
  where user_id = p_user_id
  order by created_at desc
  limit 1
) latest_weight;


  -- Last sync
  select max(last_synced_at)
  into v_last_synced
  from user_module_sync
  where user_id = p_user_id
    and module_name = 'steps';


  -- Calories burned from steps using the provided formula
  v_calories_burned := round((3.0 * v_weight * (v_steps * 0.000762 / 5))::numeric);
  
  -- Calculate age
  v_age := date_part('year', age(current_date, v_birthdate));

  -- Calculate BMR using Mifflin-St Jeor Equation
  if v_gender = 'male' then
    v_bmr := 10 * v_weight + 6.25 * v_height - 5 * v_age + 5;
  else
    v_bmr := 10 * v_weight + 6.25 * v_height - 5 * v_age - 161;
  end if;

  v_bmr_hourly := v_bmr / 24;

  -- Time since midnight today
  v_hours_today := extract(epoch from (
    timezone(v_user_timezone, now()) - date_trunc('day', timezone(v_user_timezone, now()))
  )) / 3600;



  -- Total basal burn
  v_basal_burn := round((v_bmr_hourly * v_hours_today)::numeric);

  -- Cap basal burn to full-day BMR
  if v_basal_burn > v_bmr then
    v_basal_burn := round(v_bmr::numeric);
  end if;

  -- Activities log
  select coalesce(
    jsonb_agg(obj order by (obj->>'time')::timestamptz),
    '[]'::jsonb
  )
  into v_activities
  from (
    -- Meals
    select jsonb_build_object(
      'type', 'meal',
      'time', created_at,
      'meal_type', meal_type,
      'meal_name', meal_name,
      'total_calories', round(total_calories::numeric),
      'protein_grams', round(protein_grams::numeric),
      'carbs_grams', round(carbs_grams::numeric),
      'fat_grams', round(fat_grams::numeric)
    ) as obj
    from meal_logs
    where user_id = p_user_id
      and created_at between p_start_time and p_end_time

    union all

    -- Steps
    select jsonb_build_object(
      'type', 'step',
      'time', start_time,
      'meal_name', null,
      'total_calories', round((count * 0.04)::numeric),
      'steps', count,
      'distance_km', round((count * 0.762 / 1000.0)::numeric),
      'protein_grams', null,
      'carbs_grams', null,
      'fat_grams', null
    ) as obj
    from steps
    where user_id = p_user_id
      and start_time between p_start_time and p_end_time

    union all

    -- BMR
    select jsonb_build_object(
      'type', 'bmr',
      'time', now(),
      'meal_name', null,
      'total_calories', round(v_basal_burn::numeric),
      'protein_grams', null,
      'carbs_grams', null,
      'fat_grams', null
    ) as obj
  ) all_data;

  -- Final response
  return jsonb_build_object(
    'user_name', v_user_name,
    'steps', v_steps,
    'calories_burned', round((v_calories_burned + v_basal_burn)::numeric),
    'calories_consumed', round(v_calories_consumed::numeric),
    'target_steps', v_target_steps,
    'target_calories', round(v_target_calories::numeric),
    'target_intake_calories', round(v_target_intake_calories::numeric),
    'target_calorie_deficit', v_target_calorie_deficit, -- Added target_calorie_deficit in response
    'calorie_balance', round((v_calories_consumed - (v_calories_burned + v_basal_burn))::numeric),
    'last_synced_at', v_last_synced,
    'activities', v_activities
  );
end;$function$
