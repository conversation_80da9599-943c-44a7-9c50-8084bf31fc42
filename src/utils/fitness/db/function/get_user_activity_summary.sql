CREATE OR REPLACE FUNCTION public.get_user_activity_summary(p_user_id uuid, p_start_time timestamp with time zone, p_end_time timestamp with time zone)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$DECLARE
  v_steps integer := 0;
  v_calories_burned numeric := 0;
  v_calories_consumed numeric := 0;
  v_target_steps integer := 0;
  v_target_calories numeric := 0;
  v_target_intake_calories numeric := 0;
  v_last_synced timestamp;
  v_activities jsonb;
  v_user_name text;
  v_weight numeric;
  v_height numeric;
  v_birthdate date;
  v_gender text;
  v_age int;
  v_bmr numeric := 0;
  v_bmr_hourly numeric := 0;
  v_hours_today numeric := 0;
  v_basal_burn numeric := 0;
  v_target_calorie_deficit numeric := 0;
  v_user_timezone text;
BEGIN
  SELECT full_name
  INTO v_user_name
  FROM users
  WHERE id = p_user_id;

  SELECT COALESCE(SUM(count), 0)
  INTO v_steps
  FROM steps
  WHERE user_id = p_user_id
    AND start_time >= p_start_time
    AND end_time <= p_end_time;

  SELECT ROUND(COALESCE(SUM(total_calories), 0)::numeric)
  INTO v_calories_consumed
  FROM meal_logs
  WHERE user_id = p_user_id
    AND created_at >= p_start_time
    AND created_at <= p_end_time;

  SELECT 
    COALESCE(daily_goals_steps, 0),
    ROUND(COALESCE(target_calorie, 0)::numeric),
    ROUND(COALESCE(target_intake_calorie, 0)::numeric),
    height,
    date_of_birth,
    LOWER(gender),
    ROUND(COALESCE(target_calorie_deficit, 0)::numeric),
    timezone
  INTO 
    v_target_steps,
    v_target_calories,
    v_target_intake_calories,
    v_height,
    v_birthdate,
    v_gender,
    v_target_calorie_deficit,
    v_user_timezone
  FROM user_fitness_profiles
  WHERE user_id = p_user_id;

  SELECT weight_kg
  INTO v_weight
  FROM user_weight
  WHERE user_id = p_user_id;

  SELECT MAX(last_synced_at)
  INTO v_last_synced
  FROM user_module_sync
  WHERE user_id = p_user_id
    AND module_name = 'steps';

  v_calories_burned := ROUND((3.0 * v_weight * (v_steps * 0.000762 / 5))::numeric);
  
  v_age := DATE_PART('year', AGE(CURRENT_DATE, v_birthdate));

  IF v_gender = 'male' THEN
    v_bmr := 10 * v_weight + 6.25 * v_height - 5 * v_age + 5;
  ELSE
    v_bmr := 10 * v_weight + 6.25 * v_height - 5 * v_age - 161;
  END IF;

  v_bmr_hourly := v_bmr / 24;

  v_hours_today := EXTRACT(EPOCH FROM (
    TIMEZONE(v_user_timezone, NOW()) - DATE_TRUNC('day', TIMEZONE(v_user_timezone, NOW()))
  )) / 3600;

  v_basal_burn := ROUND((v_bmr_hourly * v_hours_today)::numeric);

  IF v_basal_burn > v_bmr THEN
    v_basal_burn := ROUND(v_bmr::numeric);
  END IF;

  SELECT COALESCE(
    JSONB_AGG(obj ORDER BY (obj->>'time')::timestamptz),
    '[]'::jsonb
  )
  INTO v_activities
  FROM (
    SELECT JSONB_BUILD_OBJECT(
      'type', 'meal',
      'time', meal_time,
      'meal_type', meal_type,
      'meal_name', meal_name,
      'total_calories', ROUND(total_calories::numeric),
      'protein_grams', ROUND(protein_grams::numeric),
      'carbs_grams', ROUND(carbs_grams::numeric),
      'fat_grams', ROUND(fat_grams::numeric)
    ) AS obj
    FROM meal_logs
    WHERE user_id = p_user_id
      AND meal_time BETWEEN p_start_time AND p_end_time

    UNION ALL

    SELECT session_data AS obj
    FROM get_hybrid_sessions(p_user_id, p_start_time, p_end_time)

    UNION ALL

    SELECT JSONB_BUILD_OBJECT(
      'type', 'bmr',
      'time', NOW(),
      'meal_type', NULL,
      'meal_name', NULL,
      'total_calories', ROUND(v_basal_burn::numeric),
      'protein_grams', NULL,
      'carbs_grams', NULL,
      'fat_grams', NULL
    ) AS obj
  ) all_data;

  RETURN JSONB_BUILD_OBJECT(
    'user_name', v_user_name,
    'steps', v_steps,
    'calories_burned', ROUND((v_calories_burned + v_basal_burn)::numeric),
    'calories_consumed', ROUND(v_calories_consumed::numeric),
    'target_steps', v_target_steps,
    'target_calories', ROUND(v_target_calories::numeric),
    'target_intake_calories', ROUND(v_target_intake_calories::numeric),
    'target_calorie_deficit', v_target_calorie_deficit,
    'calorie_balance', ROUND((v_calories_consumed - (v_calories_burned + v_basal_burn))::numeric),
    'last_synced_at', v_last_synced,
    'activities', v_activities
  );
END;$function$
