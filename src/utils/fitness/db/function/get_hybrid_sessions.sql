CREATE OR REPLACE FUNCTION public.get_hybrid_sessions(p_user_id uuid, p_start_time timestamp with time zone, p_end_time timestamp with time zone)
 RETURNS TABLE(session_data jsonb)
 LANGUAGE plpgsql
AS $function$BEGIN
RETURN QUERY
WITH lagged_data AS (
    SELECT *,
        start_time - lag(end_time) OVER (ORDER BY start_time) AS time_gap,
        count::numeric / greatest(extract(epoch FROM (end_time - start_time)) / 60, 1) AS current_intensity,
        lag(count::numeric / greatest(extract(epoch FROM (end_time - start_time)) / 60, 1)) OVER (ORDER BY start_time) AS prev_intensity
    FROM steps
    WHERE user_id = p_user_id
      AND start_time BETWEEN p_start_time AND p_end_time
      AND count >= 5
),
with_intensity_change AS (
    SELECT *,
        abs(current_intensity - prev_intensity) AS intensity_change
    FROM lagged_data
),
smart_group_marked AS (
    SELECT *,
        CASE 
            WHEN time_gap > interval '3 hours' THEN 1
            WHEN time_gap > interval '1 hour' AND prev_intensity >= 80 THEN 1
            WHEN time_gap > interval '45 minutes' AND prev_intensity BETWEEN 40 AND 80 THEN 1
            WHEN time_gap > interval '20 minutes' AND prev_intensity < 40 THEN 1
            WHEN intensity_change > 50 AND time_gap > interval '10 minutes' THEN 1
            WHEN current_intensity < 10 AND time_gap > interval '15 minutes' THEN 1
            WHEN extract(hour FROM start_time) != extract(hour FROM lag(start_time) OVER (ORDER BY start_time)) AND time_gap > interval '30 minutes' THEN 1
            ELSE 0 
        END AS is_new_session
    FROM with_intensity_change
),
numbered_sessions AS (
    SELECT *, 
           sum(is_new_session) OVER (ORDER BY start_time ROWS UNBOUNDED PRECEDING) AS smart_group
    FROM smart_group_marked
),
smart_sessions AS (
    SELECT 
        min(start_time) AS session_start,
        max(end_time) AS session_end,
        sum(count) AS session_steps,
        sum(count * 0.04) AS session_calories,
        sum(count * 0.762 / 1000.0) AS session_distance,
        extract(epoch FROM (max(end_time) - min(start_time))) / 60 AS session_duration_minutes,
        avg(current_intensity) AS avg_intensity,
        max(current_intensity) AS peak_steps_per_minute,
        count(*) AS segment_count,
        count(*) - 1 AS break_count,
        CASE 
            WHEN stddev(current_intensity) IS NULL THEN 1.0
            ELSE greatest(0, 1 - (stddev(current_intensity) / nullif(avg(current_intensity), 0)))
        END AS consistency_score,
        CASE 
            WHEN avg(current_intensity) >= 80 AND max(current_intensity) >= 100 AND sum(count) >= 200 THEN 'workout'
            WHEN avg(current_intensity) BETWEEN 40 AND 70 AND extract(hour FROM min(start_time)) IN (7,8,9,17,18,19) AND sum(count) >= 300 THEN 'commute'
            WHEN avg(current_intensity) >= 30 AND sum(count) >= 100 THEN 'walk'
            ELSE 'light'
        END AS session_type,
        CASE 
            WHEN avg(current_intensity) >= 80 AND sum(count) >= 400 THEN 'excellent'
            WHEN avg(current_intensity) >= 60 AND sum(count) >= 200 THEN 'high'
            WHEN avg(current_intensity) >= 40 AND sum(count) >= 100 THEN 'medium'
            WHEN avg(current_intensity) >= 20 THEN 'low'
            ELSE 'minimal'
        END AS session_quality,
        array_to_json(array_remove(ARRAY[
            CASE WHEN max(current_intensity) >= 120 THEN 'High intensity peak detected' END,
            CASE WHEN count(*) >= 10 THEN 'Long sustained activity' END,
            CASE WHEN stddev(current_intensity) < 10 THEN 'Very consistent pace' END,
            CASE WHEN sum(count) >= 1000 THEN 'Significant step count' END
        ], NULL)) AS session_insights,
        smart_group
    FROM numbered_sessions
    GROUP BY smart_group
    HAVING sum(count) >= 20
    ORDER BY min(start_time)
)
SELECT jsonb_build_object(
    'session_type', CASE 
        WHEN session_type = 'workout' THEN 'workout_session'
        WHEN session_type = 'walk' THEN 'walking_session'
        WHEN session_type = 'commute' THEN 'commute_session'
        ELSE 'light_activity'
    END,
    'session_type_name', CASE 
        WHEN session_type = 'workout' THEN 'Workout'
        WHEN session_type = 'walk' THEN 'Walking'
        WHEN session_type = 'commute' THEN 'Commute'
        ELSE 'Light Activity'
    END,
    'type', 'step',
    'time', session_start,
    'sort_time', extract(epoch FROM session_start),
    'session_duration_minutes', round(session_duration_minutes),
    'total_steps', session_steps,
    'total_calories', round(session_calories::numeric),
    'distance_km', round(session_distance::numeric, 2),
    'avg_steps_per_minute', round(avg_intensity, 1),
    'peak_intensity', round(peak_steps_per_minute, 1),
    'session_quality', session_quality,
    'consistency_score', round(consistency_score, 2),
    'break_count', break_count,
    'time_of_day', extract(hour FROM session_start),
    'icon', CASE 
        WHEN session_type = 'workout' THEN '🏃'
        WHEN session_type = 'walk' THEN '🚶'
        WHEN session_type = 'commute' THEN '🚇'
        ELSE '👟'
    END,
    'insights', session_insights
) AS session_data
FROM smart_sessions;
END;$function$
