import React from 'react';
import { Dimensions, ScrollView, StyleSheet, Text, View } from 'react-native';
import { FitnessScreenProps } from '../../../../@types/navigation';
import { User } from '../../../assets/images';
import { PrimaryBtn } from '../../../components/Buttons/Btns';
import Header from '../../../components/Header/Header';
import { useTheme } from '../../../theme';
import { fontStyles } from '../../../theme/fonts';
import { showSuccessToast } from '../../../utils/functions';

const { width } = Dimensions.get('window');

interface CoachData {
  id: string;
  name: string;
  subtitle: string;
  quote: string;
  hashtags: string[];
  description: string;
  image?: any; // Using placeholder for now
}

const COACH_DATA: CoachData = {
  id: 'alex',
  name: '<PERSON>',
  subtitle: 'The Consistency King',
  quote: '"Small steps every day lead to big changes over time."',
  hashtags: [
    '#StayOnTrack',
    '#ConsistentWins',
    '#DailyHabits',
    '#ProgressNotPerfection',
  ],
  description:
    '<PERSON> focuses on building sustainable habits that stick. He believes in the power of consistency over intensity and helps you create a routine that fits your lifestyle.',
};

const CoachPreferenceScreen: React.FC<
  FitnessScreenProps<'CoachPreference'>
> = ({ navigation }) => {
  const colors = useTheme();

  const handleSelectCoach = () => {
    // Handle coach selection logic here
    console.log('Coach selected:', COACH_DATA.name);

    // Show success message
    showSuccessToast(`${COACH_DATA.name} has been selected as your AI coach!`);

    // Navigate back after a short delay
    setTimeout(() => {
      navigation.goBack();
    }, 1000);
  };

  const renderHashtagBadge = (hashtag: string, index: number) => (
    <View
      key={index}
      style={[styles.hashtagBadge, { borderColor: '#D1D5DB' }]} // Tailwind gray-300
    >
      <Text style={[styles.hashtagText, { color: colors.primary_spinach }]}>
        {hashtag}
      </Text>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: colors.primary_cream }]}>
      <Header headerTitle="AI Coach" showDivider={false} />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Section Title */}
        <View style={styles.titleSection}>
          <Text style={[fontStyles.Maison_600_24PX_30LH, styles.sectionTitle]}>
            Introducing Your Personal AI Mentor{' '}
          </Text>
          <Text
            style={[
              fontStyles.Maison_400_16PX_20LH,
              styles.sectionDescription,
              { color: colors.grey_600 },
            ]}
          >
            Find a mentor that resonates with your energy and aspirations.
          </Text>
        </View>

        {/* Coach Card */}
        <View
          style={[styles.coachCard, { backgroundColor: colors.neutral_white }]}
        >
          {/* Profile Image */}
          <View style={styles.imageContainer}>
            <View
              style={[
                styles.imagePlaceholder,
                { backgroundColor: colors.grey_200 },
              ]}
            >
              <User width={40} height={40} color={colors.grey_600} />
            </View>
          </View>

          {/* Coach Name */}
          <Text style={[fontStyles.Maison_600_24PX_30LH, styles.coachName]}>
            {COACH_DATA.name}
          </Text>

          {/* Subtitle */}
          <Text
            style={[
              fontStyles.Maison_500_16PX_20LH,
              styles.coachSubtitle,
              { color: colors.primary_spinach },
            ]}
          >
            {COACH_DATA.subtitle}
          </Text>

          {/* Quote */}
          <Text
            style={[
              fontStyles.Maison_400_16PX_20LH,
              styles.quote,
              { color: colors.grey_700 },
            ]}
          >
            {COACH_DATA.quote}
          </Text>

          {/* Hashtags */}
          <View style={styles.hashtagContainer}>
            {COACH_DATA.hashtags.map((hashtag, index) =>
              renderHashtagBadge(hashtag, index),
            )}
          </View>

          {/* Description */}
          <Text
            style={[
              fontStyles.Maison_400_14PX_16LH,
              styles.description,
              { color: colors.grey_600 },
            ]}
          >
            {COACH_DATA.description}
          </Text>

          {/* Select Button */}
          <PrimaryBtn
            text={`Select ${COACH_DATA.name}`}
            onPress={handleSelectCoach}
            style={[
              styles.selectButton,
              { backgroundColor: colors.primary_grenade },
            ]}
            textStyle={[
              fontStyles.Maison_600_16PX_20LH,
              { color: colors.neutral_white },
            ]}
          />
        </View>

        {/* Bottom Note */}
        <Text
          style={[
            fontStyles.Maison_400_12PX_14LH,
            styles.bottomNote,
            { color: colors.grey_600 },
          ]}
        >
          Don't worry! You can switch your coach anytime.
        </Text>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  titleSection: {
    marginTop: 24,
    marginBottom: 32,
    // alignItems: 'center',
  },
  sectionTitle: {
    textAlign: 'left',
    marginBottom: 12,
  },
  sectionDescription: {
    // textAlign: 'center',
    lineHeight: 22,
  },
  coachCard: {
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
    marginBottom: 24,
  },
  imageContainer: {
    marginBottom: 20,
  },
  imagePlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  coachName: {
    marginBottom: 8,
  },
  coachSubtitle: {
    marginBottom: 16,
    fontWeight: '600',
    fontSize: 16,
  },
  quote: {
    textAlign: 'center',
    fontStyle: 'italic',
    marginBottom: 20,
    paddingHorizontal: 16,
  },
  hashtagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginBottom: 20,
    gap: 8,
  },
  hashtagBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderWidth: 1,
    borderRadius: 16,
    margin: 4,
  },
  hashtagText: {
    fontSize: 12,
    fontWeight: '500',
  },
  description: {
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
    paddingHorizontal: 8,
  },
  selectButton: {
    width: '100%',
    borderRadius: 12,
    paddingVertical: 16,
  },
  bottomNote: {
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default CoachPreferenceScreen;
