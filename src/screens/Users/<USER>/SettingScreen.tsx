import { GoogleSignin } from '@react-native-google-signin/google-signin';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { ScrollView, Text, TextStyle, View, ViewStyle } from 'react-native';
import { FitnessScreenProps } from '../../../../@types/navigation';
import {
  Bell,
  Feedback,
  Headphone,
  InfoColoredIcon,
  KcaConsumedIcon3,
  Walk,
  Weight,
} from '../../../assets/images';
import GoalBottomSheet from '../../../components/BottomSheet/GoalBottomSheet';
import { PrimaryBtn } from '../../../components/Buttons/Btns';
import { useUserId } from '../../../components/FitnessComponent/hooks/useUserId';

import {
  CardBorder,
  CardTitle,
  CardValueContent,
  GreetingCard,
} from '../../../components/Settings/Card';
import { useTheme } from '../../../theme';
import { fontStyles } from '../../../theme/fonts';
import {
  gapStyles,
  paddingStyles,
} from '../../../theme/styles/commonMarginPadding';
import {
  borderWidthStyles,
  globalStyles,
} from '../../../theme/styles/globalStyles';
import {
  getDataFromAsyncStorage,
  removeItemFromAsyncStorage,
  showErrorToast,
  showSuccessToast,
  storeDataToAsyncStorage,
} from '../../../utils/functions';
import {
  getCoachData,
  supabase,
  updateUserFitenessProfile,
} from '../../fitness/supabaseClient';

// Type definitions
interface Theme {
  primary_cream: string;
  grey_7A7A7A: string;
  primary_grenade: string;
  transparent: string;
}

// Use the actual SvgProps from react-native-svg
interface SvgIconProps {
  height?: string | number;
  width?: string | number;
  color?: string;
  [key: string]: any; // Allow other SVG props
}

type IconComponent = React.ComponentType<SvgIconProps>;

interface UserData {
  name: string;
  email: string;
  phone?: string;
}

interface BaseItem {
  Icon: any;
  text: string;
  showBorder: boolean;
}

interface GoalItem extends BaseItem {
  id: 'food' | 'steps' | 'weight';
  value: string;
}

interface AppSettingItem extends BaseItem {
  id: 'coach' | 'health';
  value: string;
}

interface SupportItem extends BaseItem {
  id: 'feedback' | 'contact' | 'about';
}

type GoalId = GoalItem['id'];
type SettingId = AppSettingItem['id'];
type SupportId = SupportItem['id'];

// Event handler types
type GoalPressHandler = (goalId: GoalId) => void;
type SettingPressHandler = (settingId: SettingId) => void;
type SupportPressHandler = (supportId: SupportId) => void;
type LogoutHandler = () => void;

// Component prop types
interface UserProfileProps {
  color: Theme;
  userData: UserData;
}

interface GoalsSectionProps {
  color: Theme;
  onGoalPress: GoalPressHandler;
  goalValues: {
    food: string;
    steps: string;
    weight: string;
  };
}

interface AppSettingsSectionProps {
  color: Theme;
  onSettingPress: SettingPressHandler;
}

interface SupportSectionProps {
  color: Theme;
  onSupportPress: SupportPressHandler;
}

interface LogoutButtonProps {
  color: Theme;
  onLogout: LogoutHandler;
}

// USER_DATA removed - now using dynamic data from localStorage

// GOALS_DATA removed - now using dynamic data from localStorage

const APP_SETTINGS_DATA: readonly AppSettingItem[] = [
  {
    id: 'coach',
    Icon: Bell,
    text: 'Coach Preference',
    value: 'Alex',
    showBorder: true,
  },
  {
    id: 'health',
    Icon: Bell,
    text: 'Health data',
    value: 'Apple Health connected',
    showBorder: false,
  },
] as const;

const SUPPORT_DATA: readonly SupportItem[] = [
  {
    id: 'feedback',
    Icon: Feedback,
    text: 'Send feedback',
    showBorder: true,
  },
  {
    id: 'contact',
    Icon: Headphone,
    text: 'Contact us',
    showBorder: true,
  },
  {
    id: 'about',
    Icon: InfoColoredIcon,
    text: 'About',
    showBorder: false,
  },
] as const;

// Memoized components with proper typing
const UserProfile: React.FC<UserProfileProps> = React.memo(
  ({ color, userData }) => (
    <CardBorder style={globalStyles.flexDirectionRow}>
      <View style={gapStyles.gap_16}>
        <Text style={fontStyles.Maison_600_18PX_22LH}>{userData.name}</Text>
        <Text
          style={[
            fontStyles.Maison_400_14PX_16LH,
            { color: color.grey_7A7A7A } as TextStyle,
          ]}
        >
          {userData.email}
        </Text>
      </View>
    </CardBorder>
  ),
);

UserProfile.displayName = 'UserProfile';

const GoalsSection: React.FC<GoalsSectionProps> = React.memo(
  ({ color, onGoalPress, goalValues }) => {
    const dynamicGoalsData: readonly GoalItem[] = [
      {
        id: 'food',
        Icon: KcaConsumedIcon3,
        text: 'Food',
        value: goalValues.food,
        showBorder: true,
      },
      {
        id: 'steps',
        Icon: Walk,
        text: 'Steps',
        value: goalValues.steps,
        showBorder: true,
      },
      {
        id: 'weight',
        Icon: Weight,
        text: 'Weight',
        value: goalValues.weight,
        showBorder: false,
      },
    ] as const;

    return (
      <CardBorder>
        <CardTitle title="Manage goals" />
        {dynamicGoalsData.map((goal: GoalItem) => (
          <CardValueContent
            key={goal.id}
            Icon={
              <goal.Icon height={25} width={25} color={color.primary_grenade} />
            }
            text={goal.text}
            value={goal.value}
            showBorder={goal.showBorder}
            onPress={() => onGoalPress(goal.id)}
          />
        ))}
      </CardBorder>
    );
  },
);

GoalsSection.displayName = 'GoalsSection';

const AppSettingsSection: React.FC<AppSettingsSectionProps> = React.memo(
  ({ color, onSettingPress }) => (
    <CardBorder>
      <CardTitle title="App Settings" />
      {APP_SETTINGS_DATA.map((setting: AppSettingItem) => (
        <CardValueContent
          key={setting.id}
          Icon={
            <setting.Icon
              height={20}
              width={20}
              color={color.primary_grenade}
            />
          }
          text={setting.text}
          value={setting.value}
          showBorder={setting.showBorder}
          onPress={() => onSettingPress(setting.id)}
        />
      ))}
    </CardBorder>
  ),
);

AppSettingsSection.displayName = 'AppSettingsSection';

const SupportSection: React.FC<SupportSectionProps> = React.memo(
  ({ color, onSupportPress }) => (
    <CardBorder>
      {SUPPORT_DATA.map((item: SupportItem) => (
        <CardValueContent
          key={item.id}
          Icon={
            <item.Icon height={20} width={20} color={color.primary_grenade} />
          }
          text={item.text}
          showBorder={item.showBorder}
          onPress={() => onSupportPress(item.id)}
        />
      ))}
    </CardBorder>
  ),
);

SupportSection.displayName = 'SupportSection';

const LogoutButton: React.FC<LogoutButtonProps> = React.memo(
  ({ color, onLogout }) => (
    <PrimaryBtn
      style={[
        {
          backgroundColor: color.transparent,
          borderColor: color.primary_grenade,
        } as ViewStyle,
        borderWidthStyles.bw2,
      ]}
      text="Logout"
      textStyle={[
        { color: color.primary_grenade } as TextStyle,
        fontStyles.Maison_600_18PX_22LH,
      ]}
      onPress={onLogout}
    />
  ),
);

LogoutButton.displayName = 'LogoutButton';

const SettingScreen: React.FC<FitnessScreenProps<'Settings'>> = ({
  navigation,
  route,
}) => {
  const color = useTheme() as Theme;
  const userId = useUserId();
  // const navigation = useNavigation<
  //   StackNavigationProp<
  //     {
  //       Login_: { tokenRefresher: string };
  //     },
  //     'Login_'
  //   >
  // >();
  const coachData = useCallback(async () => {
    return getCoachData();
  }, []);
  useEffect(() => {
    const fetchData = async () => {
      const data = await coachData();
      console.log('coachData', data);
    };

    fetchData();
  }, [coachData]);

  // Bottom sheet state
  const [isGoalBottomSheetOpen, setIsGoalBottomSheetOpen] = useState(false);
  const [selectedGoalType, setSelectedGoalType] = useState<
    'food' | 'steps' | 'weight'
  >('food');

  // Goal values from localStorage
  const [goalValues, setGoalValues] = useState({
    food: '2000 kcal/day',
    steps: '6000 steps/day',
    weight: '65kg',
  });

  // User data from localStorage
  const [userData, setUserData] = useState<UserData>({
    name: 'Loading...',
    email: 'Loading...',
  });

  // Current numeric value for the selected goal type
  const [currentGoalNumericValue, setCurrentGoalNumericValue] =
    useState<number>(2000);
  // Memoized styles with proper typing

  const containerStyle = useMemo(
    (): ViewStyle[] => [
      paddingStyles.p20,
      globalStyles.flex1,
      { backgroundColor: color.primary_cream },
    ],
    [color.primary_cream],
  );

  // Optimized event handlers with proper typing
  const handleGoalPress = useCallback<GoalPressHandler>(
    async (goalId: GoalId) => {
      console.log(`Goal pressed: ${goalId}`);
      setSelectedGoalType(goalId);

      // Load current numeric value for the selected goal
      const currentValue = await getCurrentGoalValue(goalId);
      setCurrentGoalNumericValue(currentValue);

      setIsGoalBottomSheetOpen(true);
      // Note: Removed setModalVisible call as it was not defined

      // Add navigation or modal logic here
      switch (goalId) {
        case 'food':
          // Handle food goal navigation
          break;
        case 'steps':
          // Handle steps goal navigation
          break;
        case 'weight':
          // Handle weight goal navigation
          break;
        default:
          // TypeScript ensures this case is never reached
          const _exhaustiveCheck: never = goalId;
          return _exhaustiveCheck;
      }
    },
    [getCurrentGoalValue],
  );

  const handleSettingPress = useCallback<SettingPressHandler>(
    (settingId: SettingId) => {
      console.log(`Setting pressed: ${settingId}`);
      // Add navigation or modal logic here
      switch (settingId) {
        case 'coach':
          // Handle coach preference navigation
          navigation.navigate('CoachPreference');
          break;
        case 'health':
          // Handle health data navigation
          break;
        default:
          // TypeScript ensures this case is never reached
          const _exhaustiveCheck: never = settingId;
          return _exhaustiveCheck;
      }
    },
    [navigation],
  );

  const handleSupportPress = useCallback<SupportPressHandler>(
    (supportId: SupportId) => {
      console.log(`Support pressed: ${supportId}`);
      // Add navigation or modal logic here
      switch (supportId) {
        case 'feedback':
          // Handle feedback navigation
          navigation.navigate('SendFeedback');
          break;
        case 'contact':
          // Handle contact us navigation
          break;
        case 'about':
          // Handle about navigation
          break;
        default:
          // TypeScript ensures this case is never reached
          const _exhaustiveCheck: never = supportId;
          return _exhaustiveCheck;
      }
    },
    [navigation],
  );

  const handleLogout = useCallback<LogoutHandler>(async () => {
    try {
      await supabase.auth.signOut(); // Supabase sign out
      await GoogleSignin.revokeAccess(); // Revoke Google access
      await GoogleSignin.signOut(); // Google sign out
      await removeItemFromAsyncStorage('dashboardData');
      await removeItemFromAsyncStorage('isNewUser');
      await removeItemFromAsyncStorage('userId');

      navigation.navigate('Mealdata_', { tokenRefresher: 'tokenRefresh' });
    } catch (error) {
      console.error('Error signing out:', error);
      showErrorToast('Failed to sign out. Please try again.');
    }
  }, []);

  // Helper function to get numeric value for GoalBottomSheet
  const getCurrentGoalValue = useCallback(
    async (goalType: 'food' | 'steps' | 'weight'): Promise<number> => {
      try {
        const dashboardDataString =
          await getDataFromAsyncStorage('dashboardData');
        if (dashboardDataString) {
          const dashboardData = JSON.parse(dashboardDataString);
          switch (goalType) {
            case 'food':
              return dashboardData.target_intake_calories || 2000;
            case 'steps':
              return dashboardData.target_steps || 6000;
            case 'weight':
              return dashboardData.target_weight || 65;
            default:
              return 0;
          }
        }
      } catch (error) {
        console.error('Error getting current goal value:', error);
      }
      // Fallback values
      return goalType === 'food' ? 2000 : goalType === 'steps' ? 6000 : 65;
    },
    [],
  );

  // Helper function to load all data from localStorage
  const loadAllData = useCallback(async () => {
    try {
      const dashboardDataString =
        await getDataFromAsyncStorage('dashboardData');
      if (dashboardDataString) {
        const dashboardData = JSON.parse(dashboardDataString);

        // Load user data
        setUserData({
          name: dashboardData.user_name || 'User',
          email: dashboardData.user_email || '<EMAIL>',
        });

        // Load goal values
        setGoalValues({
          food: `${dashboardData.target_intake_calories || 2000} kcal/day`,
          steps: `${(dashboardData.target_steps || 6000).toLocaleString()} steps/day`,
          weight: `${dashboardData.target_weight || 65}kg`,
        });
      }
    } catch (error) {
      console.error('Error loading data from localStorage:', error);
    }
  }, []);

  // Helper function to update localStorage data
  const updateLocalStorageGoal = useCallback(
    async (goalType: GoalId, value: number) => {
      try {
        const dashboardDataString =
          await getDataFromAsyncStorage('dashboardData');
        if (dashboardDataString) {
          const dashboardData = JSON.parse(dashboardDataString);

          // Update the appropriate field based on goal type
          switch (goalType) {
            case 'steps':
              dashboardData.target_steps = value;
              break;
            case 'food':
              dashboardData.target_intake_calories = value;
              break;
            case 'weight':
              // Weight might not be in localStorage structure, but adding for completeness
              dashboardData.target_weight = value;
              break;
          }

          // Save updated data back to localStorage
          await storeDataToAsyncStorage(
            'dashboardData',
            JSON.stringify(dashboardData),
          );
          console.log(`Updated localStorage ${goalType} goal to:`, value);

          // Update the displayed values immediately
          await loadAllData();
        }
      } catch (error) {
        console.error('Error updating localStorage goal:', error);
      }
    },
    [loadAllData],
  );

  // Load data on component mount
  useEffect(() => {
    loadAllData();
    // Also initialize the current numeric value for the default selected goal type
    getCurrentGoalValue(selectedGoalType).then(setCurrentGoalNumericValue);
  }, [loadAllData, getCurrentGoalValue, selectedGoalType]);

  // Bottom sheet handlers
  const handleCloseGoalBottomSheet = useCallback(() => {
    setIsGoalBottomSheetOpen(false);
  }, []);

  const handleSaveGoal = useCallback(
    async (value: number) => {
      console.log(`Saving ${selectedGoalType} goal:`, value);
      getDataFromAsyncStorage('dashboardData').then((data) => {
        console.log('Dashboard data:', data);
      });

      try {
        if (selectedGoalType === 'weight' && userId) {
          // The value received is always in kg from GoalBottomSheet
          const weightKg = value;

          // Update weight in the database with both kg and lb
          const success = await updateUserFitenessProfile(
            userId?.userId as string,
            { target_weight: weightKg },
          );

          if (success) {
            // Update localStorage after successful database update
            await updateLocalStorageGoal('weight', weightKg);
            showSuccessToast(`Your weight has been updated to ${weightKg} kg`);
          } else {
            showErrorToast('Failed to update your weight. Please try again.');
          }
        } else if (selectedGoalType === 'food' && userId) {
          // The value received is always in grams from GoalBottomSheet
          const grams = value;
          // Update food intake in the database with grams
          const success = await updateUserFitenessProfile(
            userId?.userId as string,
            { target_intake_calorie: grams },
          );
          if (success) {
            // Update localStorage after successful database update
            await updateLocalStorageGoal('food', grams);
            showSuccessToast('Calorie goal updated successfully');
          } else {
            showErrorToast(
              'Failed to update your calorie goal. Please try again.',
            );
          }
        } else if (selectedGoalType === 'steps' && userId) {
          // The value received is always in steps from GoalBottomSheet
          const steps = value;
          // Update steps in the database with steps
          const success = await updateUserFitenessProfile(
            userId?.userId as string,
            { daily_goals_steps: steps },
          );

          if (success) {
            // Update localStorage after successful database update
            await updateLocalStorageGoal('steps', steps);
            showSuccessToast(`Your steps goal has been updated to ${steps}`);
          } else {
            showErrorToast(
              'Failed to update your steps goal. Please try again.',
            );
          }
        } else {
          showSuccessToast(
            `Your ${selectedGoalType} goal has been updated to ${value}`,
          );
        }
      } catch (error) {
        console.error('Error updating goal:', error);
        showErrorToast(
          'An error occurred while updating your goal. Please try again.',
        );
      }
    },
    [selectedGoalType, userId, updateLocalStorageGoal],
  );
  return (
    <ScrollView
      removeClippedSubviews
      showsVerticalScrollIndicator={false}
      bounces={false}
    >
      <View style={containerStyle}>
        <View style={gapStyles.gap_16}>
          <GreetingCard />

          <UserProfile color={color} userData={userData} />

          <GoalsSection
            color={color}
            onGoalPress={handleGoalPress}
            goalValues={goalValues}
          />

          <AppSettingsSection
            color={color}
            onSettingPress={handleSettingPress}
          />

          <SupportSection color={color} onSupportPress={handleSupportPress} />

          <LogoutButton color={color} onLogout={handleLogout} />
        </View>
      </View>

      {/* Goal Bottom Sheet */}
      <GoalBottomSheet
        isOpen={isGoalBottomSheetOpen}
        onClose={handleCloseGoalBottomSheet}
        goalType={selectedGoalType}
        onSave={handleSaveGoal}
        currentValue={currentGoalNumericValue}
      />
    </ScrollView>
  );
};

export default SettingScreen;
