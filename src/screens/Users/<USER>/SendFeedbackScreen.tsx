import React, { useCallback, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>rollView,
  Text,
  TextInput,
  TextStyle,
  View,
  ViewStyle,
} from 'react-native';
import { FitnessScreenProps } from '../../../../@types/navigation';
import { PrimaryBtn } from '../../../components/Buttons/Btns';
import { SelectableRoundChip } from '../../../components/Buttons/Chips';
import { ScreenHeader } from '../../../components/ScreenHeader/ScreenHeader';
import { useTheme } from '../../../theme';
import { fontStyles } from '../../../theme/fonts';
import {
  gapStyles,
  marginStyles,
  paddingStyles,
} from '../../../theme/styles/commonMarginPadding';
import {
  borderRadiusStyles,
  globalStyles,
} from '../../../theme/styles/globalStyles';
import { showErrorToast, showSuccessToast } from '../../../utils/functions';

// Type definitions
interface Theme {
  primary_cream: string;
  grey_7A7A7A: string;
  primary_grenade: string;
  neutral_white: string;
  grey_100: string;
  grey_700: string;
  grey_600: string;
  primary_spinach: string;
  neutral_black: string;
}

interface FeedbackOption {
  id: string;
  label: string;
}

const SCREEN_OPTIONS: FeedbackOption[] = [
  { id: 'home', label: 'Home Screen' },
  { id: 'profile', label: 'Profile Settings' },
  { id: 'notifications', label: 'Notifications' },
  { id: 'coach', label: 'Coach' },
  { id: 'other_screen', label: 'Other' },
];

const FEEDBACK_TYPE_OPTIONS: FeedbackOption[] = [
  { id: 'bug', label: 'Bug' },
  { id: 'feature', label: 'Feature Request' },
  { id: 'ux', label: 'UX Issue' },
  { id: 'other_type', label: 'Other' },
];

const SendFeedbackScreen: React.FC<FitnessScreenProps<'SendFeedback'>> = ({
  navigation,
}) => {
  const color = useTheme() as Theme;

  // State management
  const [selectedScreen, setSelectedScreen] = useState<string>('home');
  const [selectedFeedbackType, setSelectedFeedbackType] =
    useState<string>('bug');
  const [feedbackText, setFeedbackText] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  // Styles
  const containerStyle: ViewStyle[] = [
    paddingStyles.p20,
    globalStyles.flex1,
    { backgroundColor: color.primary_cream },
  ];

  const sectionStyle: ViewStyle[] = [gapStyles.gap_12, marginStyles.mb_24];

  const sectionTitleStyle: TextStyle[] = [
    fontStyles.Maison_600_18PX_22LH,
    { color: color.neutral_black },
    marginStyles.mb_12,
  ];

  const descriptionStyle: TextStyle[] = [
    fontStyles.Maison_400_16PX_20LH,
    { color: color.grey_700 },
    marginStyles.mb_24,
  ];

  const textAreaStyle: ViewStyle[] = [
    {
      backgroundColor: '#F7F7F7',
      borderColor: color.grey_100,
      borderWidth: 1,
      minHeight: 120,
    },
    borderRadiusStyles.br12,
    paddingStyles.p16,
  ];

  const textAreaInputStyle: TextStyle[] = [
    fontStyles.Maison_400_16PX_20LH,
    { color: color.grey_700, textAlignVertical: 'top' },
    globalStyles.flex1,
  ];

  const pillContainerStyle: ViewStyle[] = [
    globalStyles.flexDirectionRow,
    globalStyles.flexWrap,
    gapStyles.gap_8,
  ];

  // Event handlers
  const handleScreenSelect = useCallback((screenId: string) => {
    setSelectedScreen(screenId);
  }, []);

  const handleFeedbackTypeSelect = useCallback((typeId: string) => {
    setSelectedFeedbackType(typeId);
  }, []);

  const handleSubmitFeedback = useCallback(async () => {
    if (!feedbackText.trim()) {
      showErrorToast('Please describe your feedback before submitting.');
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call - replace with actual implementation
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Here you would typically send the feedback to your backend
      const feedbackData = {
        screen: selectedScreen,
        type: selectedFeedbackType,
        description: feedbackText.trim(),
        timestamp: new Date().toISOString(),
      };

      console.log('Feedback submitted:', feedbackData);

      // Show success message
      showSuccessToast('Feedback submitted successfully!');

      // Reset form
      setFeedbackText('');
      setSelectedScreen('home');
      setSelectedFeedbackType('bug');

      // Navigate back after a short delay
      setTimeout(() => {
        navigation.goBack();
      }, 1000);
    } catch (error) {
      console.error('Error submitting feedback:', error);
      showErrorToast('Failed to submit feedback. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  }, [feedbackText, selectedScreen, selectedFeedbackType, navigation]);

  const handleGoBack = useCallback(() => {
    if (feedbackText.trim()) {
      Alert.alert(
        'Discard Feedback?',
        'You have unsaved changes. Are you sure you want to go back?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Discard',
            style: 'destructive',
            onPress: () => navigation.goBack(),
          },
        ],
      );
    } else {
      navigation.goBack();
    }
  }, [feedbackText, navigation]);

  return (
    <View style={containerStyle}>
      <ScreenHeader
        title="Send feedback"
        isBack={true}
        onPressBack={handleGoBack}
        wrapperStyles={marginStyles.mb_24}
      />

      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={paddingStyles.pb20}
      >
        {/* Description */}
        <Text style={descriptionStyle}>
          Tell us what you liked, what didn't work, or what you'd love to see
          improved. We're listening!
        </Text>

        {/* Screen Selection Section */}
        <View style={sectionStyle}>
          <Text style={sectionTitleStyle}>What screen were you on?</Text>
          <View style={pillContainerStyle}>
            {SCREEN_OPTIONS.map((option) => (
              <SelectableRoundChip
                key={option.id}
                text={option.label}
                isSelected={selectedScreen === option.id}
                onPress={() => handleScreenSelect(option.id)}
                style={marginStyles.mb_8}
                textStyle={fontStyles.Maison_500_14PX_18LH}
              />
            ))}
          </View>
        </View>

        {/* Feedback Type Selection Section */}
        <View style={sectionStyle}>
          <Text style={sectionTitleStyle}>What type of feedback is this?</Text>
          <View style={pillContainerStyle}>
            {FEEDBACK_TYPE_OPTIONS.map((option) => (
              <SelectableRoundChip
                key={option.id}
                text={option.label}
                isSelected={selectedFeedbackType === option.id}
                onPress={() => handleFeedbackTypeSelect(option.id)}
                style={marginStyles.mb_8}
                textStyle={fontStyles.Maison_500_14PX_18LH}
              />
            ))}
          </View>
        </View>

        {/* Feedback Text Area */}
        <View style={sectionStyle}>
          <Text style={sectionTitleStyle}>Describe your feedback</Text>
          <View style={textAreaStyle}>
            <TextInput
              style={textAreaInputStyle}
              placeholder="E.g. I couldn't understand the stats shown in home screen"
              placeholderTextColor={color.grey_600}
              value={feedbackText}
              onChangeText={setFeedbackText}
              multiline={true}
              numberOfLines={6}
              textAlignVertical="top"
            />
          </View>
        </View>

        {/* Submit Button */}
        <PrimaryBtn
          text="Submit Feedback"
          onPress={handleSubmitFeedback}
          isLoading={isSubmitting}
          disabled={isSubmitting}
          style={[
            marginStyles.mt_32,
            {
              backgroundColor: color.primary_grenade,
            } as ViewStyle,
          ]}
          textStyle={[
            fontStyles.Maison_600_18PX_22LH,
            { color: color.neutral_white } as TextStyle,
          ]}
        />
      </ScrollView>
    </View>
  );
};

export default SendFeedbackScreen;
