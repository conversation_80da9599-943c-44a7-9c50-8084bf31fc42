import React, { useRef } from 'react';
import {
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { fontStyles, RFont } from '../../theme/fonts';

interface AdjustmentCounterProps {
  value: number;
  onIncrement: () => void;
  onDecrement: () => void;
  onChange: (value: number) => void;
  disabledIncrement?: boolean;
  disabledDecrement?: boolean;
  holdInterval?: number; // ms between increments on long press
}

const AdjustmentCounter: React.FC<AdjustmentCounterProps> = ({
  value,
  onIncrement,
  onDecrement,
  onChange,
  disabledIncrement = false,
  disabledDecrement = false,
  holdInterval = 200,
}) => {
  const incrementInterval = useRef<NodeJS.Timeout | null>(null);
  const decrementInterval = useRef<NodeJS.Timeout | null>(null);

  const startIncrement = () => {
    onIncrement();
    incrementInterval.current = setInterval(() => {
      onIncrement();
    }, holdInterval);
  };

  const startDecrement = () => {
    onDecrement();
    decrementInterval.current = setInterval(() => {
      onDecrement();
    }, holdInterval);
  };

  const stopIncrement = () => {
    if (incrementInterval.current) clearInterval(incrementInterval.current);
  };

  const stopDecrement = () => {
    if (decrementInterval.current) clearInterval(decrementInterval.current);
  };

  const handleManualChange = (text: string) => {
    const numericValue = parseInt(text, 10);
    if (!isNaN(numericValue)) {
      onChange(numericValue);
    } else if (text === '') {
      onChange(0); // Optional: Reset to 0 on empty
    }
  };

  return (
    <View style={styles.adjustmentContainer}>
      <TouchableOpacity
        style={[
          styles.adjustmentButton,
          disabledDecrement && styles.adjustmentButtonDisabled,
        ]}
        onPress={onDecrement}
        onPressIn={() => !disabledDecrement && startDecrement()}
        onPressOut={stopDecrement}
        disabled={disabledDecrement}
      >
        <Text
          style={[
            styles.adjustmentButtonText,
            { color: disabledDecrement ? '#aaa' : '#000' },
          ]}
        >
          −
        </Text>
      </TouchableOpacity>

      <View style={styles.currentValueContainer}>
        <TextInput
          style={styles.currentValueText}
          keyboardType="numeric"
          value={String(value)}
          onChangeText={handleManualChange}
        />
      </View>

      <TouchableOpacity
        style={[
          styles.adjustmentButton,
          disabledIncrement && styles.adjustmentButtonDisabled,
        ]}
        onPress={onIncrement}
        onPressIn={() => !disabledIncrement && startIncrement()}
        onPressOut={stopIncrement}
        disabled={disabledIncrement}
      >
        <Text
          style={[
            styles.adjustmentButtonText,
            { color: disabledIncrement ? '#aaa' : '#000' },
          ]}
        >
          +
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  adjustmentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: RFont(24),
  },
  adjustmentButton: {
    width: RFont(56),
    height: RFont(56),
    borderRadius: RFont(28),
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: RFont(20),
    borderWidth: RFont(2),
    borderColor: 'gray',
    backgroundColor: '#f5f5f5',
  },
  adjustmentButtonDisabled: {
    opacity: 0.4,
  },
  adjustmentButtonText: {
    fontSize: RFont(30),
  },
  currentValueContainer: {
    minWidth: RFont(120),
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: RFont(16),
    borderBottomWidth: 0.5,
    borderColor: 'gray',
  },
  currentValueText: {
    ...fontStyles.Maison_600_24PX_30LH,
    textAlign: 'center',
    color: 'black',
  },
});

export default AdjustmentCounter;
