import React, { useRef } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { fontStyles, RFont } from '../../theme/fonts';

interface AdjustmentCounterProps {
  value: number;
  onIncrement: () => void;
  onDecrement: () => void;
  disabledIncrement?: boolean;
  disabledDecrement?: boolean;
  holdInterval?: number; // Optional: ms between each increment when held
}

const AdjustmentCounter: React.FC<AdjustmentCounterProps> = ({
  value,
  onIncrement,
  onDecrement,
  disabledIncrement = false,
  disabledDecrement = false,
  holdInterval = 300,
}) => {
  const incrementInterval = useRef<NodeJS.Timeout | null>(null);
  const decrementInterval = useRef<NodeJS.Timeout | null>(null);

  const startIncrement = () => {
    onIncrement();
    incrementInterval.current = setInterval(() => {
      onIncrement();
    }, holdInterval);
  };

  const startDecrement = () => {
    onDecrement();
    decrementInterval.current = setInterval(() => {
      onDecrement();
    }, holdInterval);
  };

  const stopIncrement = () => {
    if (incrementInterval.current) clearInterval(incrementInterval.current);
  };

  const stopDecrement = () => {
    if (decrementInterval.current) clearInterval(decrementInterval.current);
  };

  return (
    <View style={styles.adjustmentContainer}>
      <TouchableOpacity
        style={[
          styles.adjustmentButton,
          disabledDecrement && styles.adjustmentButtonDisabled,
        ]}
        onPress={onDecrement}
        onPressIn={() => !disabledDecrement && startDecrement()}
        onPressOut={stopDecrement}
        disabled={disabledDecrement}
      >
        <Text
          style={[
            styles.adjustmentButtonText,
            { color: disabledDecrement ? '#aaa' : '#000' },
          ]}
        >
          −
        </Text>
      </TouchableOpacity>

      <View style={styles.currentValueContainer}>
        <Text style={styles.currentValueText}>{value}</Text>
      </View>

      <TouchableOpacity
        style={[
          styles.adjustmentButton,
          disabledIncrement && styles.adjustmentButtonDisabled,
        ]}
        onPress={onIncrement}
        onPressIn={() => !disabledIncrement && startIncrement()}
        onPressOut={stopIncrement}
        disabled={disabledIncrement}
      >
        <Text
          style={[
            styles.adjustmentButtonText,
            { color: disabledIncrement ? '#aaa' : '#000' },
          ]}
        >
          +
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  adjustmentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: RFont(24),
  },
  adjustmentButton: {
    width: RFont(56),
    height: RFont(56),
    borderRadius: RFont(28),
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: RFont(20),
    borderWidth: RFont(2),
    borderColor: 'gray',
    backgroundColor: '#f5f5f5',
  },
  adjustmentButtonDisabled: {
    // backgroundColor: 'gray',
    borderColor: 'gray',
    opacity: 0.4,
  },
  adjustmentButtonText: {
    fontSize: RFont(30),
  },
  currentValueContainer: {
    minWidth: RFont(120),
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: RFont(16),
    borderBottomWidth: 0.5,
    borderColor: 'gray',
  },
  currentValueText: {
    ...fontStyles.Maison_600_24PX_30LH,
    textAlign: 'center',
  },
});

export default AdjustmentCounter;
