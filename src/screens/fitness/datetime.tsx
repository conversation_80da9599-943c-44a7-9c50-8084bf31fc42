import React, { useEffect, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import DatePicker from 'react-native-date-picker';
import { GorhomBottomSheet } from '../../components/BottomSheet/BottomSheet';
import { PrimaryBtn } from '../../components/Buttons/Btns';

interface Props {
  open: boolean;
  initialDate?: Date;
  title?: string;
  mode?: 'date' | 'time' | 'datetime';
  onClose: (date: Date | null) => void;
  minimumDate?: Date;
  maximumDate?: Date;
}

const Dateandtime: React.FC<Props> = ({
  open,
  initialDate,
  onClose,
  mode = 'time',
  title = 'Select Date & Time',
  minimumDate,
  maximumDate,
}) => {
  const getValidDate = (d?: Date): Date => {
    return d instanceof Date && !isNaN(d.getTime()) ? d : new Date();
  };

  const [date, setDate] = useState<Date>(getValidDate(initialDate));
  const [hasChanged, setHasChanged] = useState(false);

  useEffect(() => {
    if (open) {
      const validDate = getValidDate(initialDate);
      setDate(validDate);
      setHasChanged(false);
    }
  }, [open, initialDate]);

  const onDateChange = (newDate: Date) => {
    setDate(newDate);
    setHasChanged(true);
  };

  const onSave = () => {
    onClose(date);
  };

  const onCancel = () => {
    const validDate = getValidDate(initialDate);
    setDate(validDate);
    setHasChanged(false);
    onClose(null);
  };

  return (
    <GorhomBottomSheet
      sheetOpen={open}
      title={title}
      closeOnBackdrop
      closeOnPressBack
      sheetClose={onCancel}
    >
      <View style={styles.container}>
        <DatePicker
          date={date}
          onDateChange={onDateChange}
          mode={mode}
          theme="light"
          maximumDate={maximumDate}
          minimumDate={minimumDate}
          style={styles.datePicker}
        />
        <PrimaryBtn
          text="Save"
          onPress={onSave}
          style={styles.saveButton}
          textStyle={styles.saveButtonText}
          disabled={!hasChanged}
        />
      </View>
    </GorhomBottomSheet>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  datePicker: {
    alignSelf: 'center',
    marginBottom: 10,
  },
  timeText: {
    textAlign: 'center',
    fontSize: 18,
    fontWeight: '500',
    marginBottom: 10,
  },
  saveButton: {
    marginTop: 12,
    marginHorizontal: 8,
  },
  saveButtonText: {
    fontSize: 16,
    lineHeight: 19,
    fontWeight: '600',
  },
});

export default Dateandtime;
