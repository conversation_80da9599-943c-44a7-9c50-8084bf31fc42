import { useNavigation } from '@react-navigation/native';
import { StackScreenProps } from '@react-navigation/stack';
import React, { useEffect, useRef } from 'react';
import {
  Animated,
  Image,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { useDispatch, useSelector } from 'react-redux';
import { FitnessRootStackParamList } from '../../../@types/fitnessnavigator';
import Minuscircle from '../../assets/images/svgs/minuscircle.svg';
import Star from '../../assets/images/svgs/StarIos.svg';
import { PrimaryBtn } from '../../components/Buttons/Btns';
import { useUserId } from '../../components/FitnessComponent/hooks/useUserId';
import Header from '../../components/Header/Header';
import { logMeal } from '../../redux/action/appActions';
import { RootState } from '../../redux/store';
import { fontStyles } from '../../theme/fonts';
import { Calorieandmacro } from './calorieandmacro';
import Dateandtime from './datetime';
import Selectmealtype from './selectmealtype';

// Add these imports to your existing imports
import { Alert } from 'react-native';
import { FoodScan, FoodScan2 } from '../../assets/images';
import { showErrorToast, showSuccessToast } from '../../utils/functions';

const Mealdata: React.FC<
  StackScreenProps<FitnessRootStackParamList, 'Mealdata'>
> = ({ route }) => {
  const dispatch = useDispatch();
  const {
    photoUri,
    fromTextAnalysis,
    textDescription,
    selectedMealType: routeMealType,
    analysisResult,
  } = route.params;
  // const result = fromTextAnalysis
  //   ? analysisResult
  //   : useSelector((state: RootState) => state.app.analyzeFoodResult);
  // Add this selector in your Mealdata component to get the text meal detection result
  const textMealResult = useSelector(
    (state: RootState) => state.app.textMealDetectionResult,
  );

  // Update the result assignment logic
  const result = fromTextAnalysis
    ? analysisResult || textMealResult // Use analysisResult if passed via route params, otherwise use textMealResult from Redux
    : useSelector((state: RootState) => state.app.analyzeFoodResult);
  const navigation = useNavigation();
  const { loading: userFetchLoading, userId } = useUserId();
  const [iscaloriesheet, setiscaloriesheet] = React.useState(false);
  const [isdateTime, setisdateTime] = React.useState(false);
  const [ismealtype, setismealtype] = React.useState(false);

  const dishName =
    result?.data?.nutrient_info?.detection?.primary_dish ?? 'Unknown Dish';
  const calories = result?.data?.nutrient_info?.display_data?.calories || 0;
  const protein = result?.data?.nutrient_info?.display_data?.protein_g || 0;
  const carbs = result?.data?.nutrient_info?.display_data?.carbs_g || 0;
  const fats = result?.data?.nutrient_info?.display_data?.fat_g || 0;

  const scanAnim = useRef(new Animated.Value(0)).current;

  // Add these state variables after your existing state declarations
  const [editableIngredients, setEditableIngredients] = React.useState<
    Array<{
      name: string;
      quantity: number;
    }>
  >([]);
  const [isAddingIngredient, setIsAddingIngredient] = React.useState(false);
  const [newIngredientName, setNewIngredientName] = React.useState('');
  const [newIngredientWeight, setNewIngredientWeight] = React.useState('');
  const [editingIngredientIndex, setEditingIngredientIndex] = React.useState<
    number | null
  >(null);

  // Add these state variables after your existing state declarations
  const [editableNutrition, setEditableNutrition] = React.useState({
    calories: calories,
    protein: protein,
    carbs: carbs,
    fats: fats,
  });

  const [selectedMealType, setSelectedMealType] = React.useState(
    fromTextAnalysis && routeMealType ? routeMealType : 'Meal',
  );
  const [selectedDateTime, setSelectedDateTime] = React.useState(new Date());

  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(scanAnim, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: true,
        }),
        Animated.timing(scanAnim, {
          toValue: 0,
          duration: 1500,
          useNativeDriver: true,
        }),
      ]),
    ).start();
  }, []);

  const translateY = scanAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 150], // Adjust based on image height
  });
  const ingredients =
    result?.data?.nutrient_info?.detailed_breakdown?.portion_breakdown?.map(
      (data) => ({
        name: data.ingredient,
        quantity: data.weight_grams,
      }),
    );

  console.log(ingredients, 'INGREDIENTS');

  console.log(userId, 'userId-->>>>>>');

  console.log(
    'DEBUG result?.data?.nutrient_info userId',
    result?.data?.nutrient_info,
    'DEBUG result?.data?.s3_image_url',
    result?.data?.s3_image_url,
    userId,
  );

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    });
  };

  // Initialize editable ingredients from the API result
  useEffect(() => {
    if (
      result?.data?.nutrient_info?.detailed_breakdown?.portion_breakdown &&
      editableIngredients.length === 0
    ) {
      const ingredientsFromAPI =
        result.data.nutrient_info.detailed_breakdown.portion_breakdown.map(
          (data) => ({
            name: data.ingredient,
            quantity: data.weight_grams,
          }),
        );
      setEditableIngredients(ingredientsFromAPI);
    }
  }, [result]);

  // Initialize editable nutrition from the API result (only once)
  useEffect(() => {
    if (
      result?.data?.nutrient_info?.display_data &&
      editableNutrition.calories === 0
    ) {
      const displayData = result.data.nutrient_info.display_data;
      setEditableNutrition({
        calories: displayData.calories || 0,
        protein: displayData.protein_g || 0,
        carbs: displayData.carbs_g || 0,
        fats: displayData.fat_g || 0,
      });
    }
  }, [result]);

  const updateIngredientWeight = (index: number, newWeight: string) => {
    // Allow empty string or valid numbers (including decimals)
    if (newWeight === '' || /^\d*\.?\d*$/.test(newWeight)) {
      const weight = newWeight === '' ? 0 : parseFloat(newWeight) || 0;
      setEditableIngredients((prev) =>
        prev.map((item, i) =>
          i === index ? { ...item, quantity: weight } : item,
        ),
      );
    }
  };

  const removeIngredient = (index: number) => {
    Alert.alert(
      'Remove Ingredient',
      'Are you sure you want to remove this ingredient?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            setEditableIngredients((prev) =>
              prev.filter((_, i) => i !== index),
            );
          },
        },
      ],
    );
  };

  const addNewIngredient = () => {
    if (!newIngredientName.trim()) {
      showErrorToast('Please enter ingredient name');
      return;
    }

    const weight = parseFloat(newIngredientWeight) || 0;
    if (weight <= 0) {
      showErrorToast('Please enter a valid weight');
      return;
    }

    setEditableIngredients((prev) => [
      ...prev,
      { name: newIngredientName.trim(), quantity: weight },
    ]);

    // Reset form
    setNewIngredientName('');
    setNewIngredientWeight('');
    setIsAddingIngredient(false);

    showSuccessToast('Ingredient added!');
  };

  // 3. Update the handleLogMeal function to use selected values:

  const handleLogMeal = () => {
    // Calculate total weight from editable ingredients
    const totalWeight = editableIngredients.reduce(
      (sum, item) => sum + item.quantity,
      0,
    );

    // Create dynamic portion breakdown from editable ingredients
    const dynamicPortionBreakdown = editableIngredients.map((ingredient) => ({
      ingredient: ingredient.name,
      weight_grams: ingredient.quantity,
      calories: Math.round(
        editableNutrition.calories * (ingredient.quantity / totalWeight),
      ),
      protein_g:
        Math.round(
          editableNutrition.protein * (ingredient.quantity / totalWeight) * 100,
        ) / 100,
      carbs_g:
        Math.round(
          editableNutrition.carbs * (ingredient.quantity / totalWeight) * 100,
        ) / 100,
      fat_g:
        Math.round(
          editableNutrition.fats * (ingredient.quantity / totalWeight) * 100,
        ) / 100,
      household_measure: `${ingredient.quantity}g`,
      estimation_notes: 'User edited ingredient',
    }));

    // Determine the correct image URL to use
    const imageUrl = fromTextAnalysis
      ? result?.data?.s3_image_url || null  // Use s3_image_url from text analysis result
      : photoUri;  // Use original photo URI for regular photo analysis

    const payload = {
      analysis_data: {
        display_data: {
          food_name: dishName,
          calories: editableNutrition.calories,
          protein_g: editableNutrition.protein,
          carbs_g: editableNutrition.carbs,
          fat_g: editableNutrition.fats,
        },
        detailed_breakdown: {
          portion_breakdown: dynamicPortionBreakdown,
          total_weight_grams: totalWeight,
          preparation_notes: 'Meal with user-edited ingredients',
        },
        confidence_metrics: {
          overall_confidence: 0.89,
          ingredient_confidence: 0.92,
          portion_confidence: 0.85,
          nutrition_confidence: 0.91,
        },
        detection: {
          food_items: editableIngredients.map((item) => item.name),
          confidence_score: 0.89,
        },
        nutrition: {
          macros: {
            calories: editableNutrition.calories,
            protein_g: editableNutrition.protein,
            carbs_g: editableNutrition.carbs,
            fat_g: editableNutrition.fats,
          },
          per_100g: {
            calories: Math.round(
              (editableNutrition.calories / totalWeight) * 100,
            ),
            protein_g:
              Math.round(
                (editableNutrition.protein / totalWeight) * 100 * 100,
              ) / 100,
            carbs_g:
              Math.round((editableNutrition.carbs / totalWeight) * 100 * 100) /
              100,
            fat_g:
              Math.round((editableNutrition.fats / totalWeight) * 100 * 100) /
              100,
          },
        },
        analysis_version: 'enhanced-multi-stage-v1.0',
        processing_time_ms: 2340,
      },
      image_url: imageUrl,
      user_id: userId,
      meal_type: selectedMealType.toLowerCase(),
      date_time: selectedDateTime.toISOString(),
      notes: fromTextAnalysis
        ? `Text analysis: ${textDescription}`
        : 'Meal with user-edited ingredients',
    };

    console.log('[LogMeal Payload - Final]', JSON.stringify(payload, null, 2));
    dispatch(logMeal(payload));
    showSuccessToast('Meal logged successfully!');
    // ToastAndroid.show('Meal logged successfully!', ToastAndroid.SHORT);
    navigation.navigate('Home');
  };

  return (
    <>
      <Header
        headerTitle={result ? 'Review Meal' : 'Scan Meal'}
        showDivider={false}
        textStyle={{ textAlign: 'center', flex: 0.9 }}
      />
      <View style={styles.headerDivider} />
      {result ? (
        <>
          <ScrollView contentContainerStyle={styles.scrollContainer}>
            <View style={styles.container}>
              <View style={styles.box}>
                <View style={styles.headerRow}>
                  <TouchableOpacity
                    style={styles.timeBox}
                    onPress={() => setisdateTime(true)} // Fixed: was opening calories sheet
                  >
                    <Text style={fontStyles.Maison_600_14PX_16_8LH}>
                      {formatTime(selectedDateTime)}
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.timeBox}
                    onPress={() => setismealtype(true)} // Fixed: now opens meal type selector
                  >
                    <Text style={fontStyles.Maison_600_14PX_16_8LH}>
                      {selectedMealType.charAt(0).toUpperCase() +
                        selectedMealType.slice(1)}
                    </Text>
                  </TouchableOpacity>
                </View>
                {/* Update the image section to handle text analysis and s3_image_url */}
                <View style={styles.imageBox}>
                  {fromTextAnalysis ? (
                    // Check if we have an s3_image_url from the text analysis result
                    result?.data?.s3_image_url ? (
                      <Image
                        source={{ uri: result.data.s3_image_url }}
                        style={styles.image}
                        onError={(error) => {
                          console.log('Error loading s3 image:', error);
                          // Fallback to text placeholder if image fails to load
                        }}
                      />
                    ) : (
                      // Show text analysis placeholder if no image URL available
                      <View style={styles.textAnalysisPlaceholder}>
                        <Text
                          style={[
                            fontStyles.Maison_400_16PX_20LH,
                            styles.textAnalysisLabel,
                          ]}
                        >
                          Text Analysis
                        </Text>
                        <Text
                          style={[
                            fontStyles.Maison_500_16PX_22LH,
                            styles.textDescription,
                          ]}
                        >
                          {textDescription}
                        </Text>
                      </View>
                    )
                  ) : (
                    // Regular photo analysis - show the captured photo
                    <Image source={{ uri: photoUri }} style={styles.image} />
                  )}
                </View>
                <View style={styles.timeBox}>
                  <Text
                    style={[fontStyles.Maison_500_20PX_28LH, styles.dishName]}
                  >
                    {dishName}
                  </Text>
                </View>
                <Text
                  style={[fontStyles.Maison_400_16PX_20LH, styles.subTitle]}
                >
                  Meal name Based on your photo
                </Text>
              </View>
              <View style={styles.box}>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: 5,
                    marginBottom: 10,
                  }}
                >
                  <Text style={fontStyles.Maison_600_18PX_24LH}>
                    Calories & macros
                  </Text>
                  <Text
                    style={[fontStyles.Maison_600_14PX_24LH, styles.editButton]}
                    onPress={() => setiscaloriesheet(true)}
                  >
                    Edit
                  </Text>
                </View>
                <View style={styles.calorieContainer}>
                  <Text
                    style={[
                      fontStyles.Maison_600_32PX_40LH,
                      styles.calorieText,
                    ]}
                  >
                    {editableNutrition.calories}
                  </Text>
                  <Text style={[fontStyles.Maison_400_16PX_22LH, styles.kcal]}>
                    kcal
                  </Text>
                </View>

                <View style={styles.macroBarContainer}>
                  <View
                    style={[
                      styles.macroSegment,
                      {
                        flex: editableNutrition.protein,
                        backgroundColor: '#F7931A',
                      },
                    ]}
                  />
                  <View
                    style={[
                      styles.macroSegment,
                      {
                        flex: editableNutrition.carbs,
                        backgroundColor: '#6B8E23',
                      },
                    ]}
                  />
                  <View
                    style={[
                      styles.macroSegment,
                      {
                        flex: editableNutrition.fats,
                        backgroundColor: '#C2B90A',
                      },
                    ]}
                  />
                </View>

                <View style={styles.macroLegendRow}>
                  <View style={styles.macroLegendColumn}>
                    <View style={styles.labelRow}>
                      <View
                        style={[styles.dot, { backgroundColor: '#F7931A' }]}
                      />
                      <Text style={styles.macroLabel}>Protein</Text>
                    </View>
                    <Text style={styles.macroValue}>
                      {editableNutrition.protein}g
                    </Text>
                  </View>

                  <View style={styles.macroLegendColumn}>
                    <View style={styles.labelRow}>
                      <View
                        style={[styles.dot, { backgroundColor: '#6B8E23' }]}
                      />
                      <Text style={styles.macroLabel}>Carbs</Text>
                    </View>
                    <Text style={styles.macroValue}>
                      {editableNutrition.carbs}g
                    </Text>
                  </View>

                  <View style={styles.macroLegendColumn}>
                    <View style={styles.labelRow}>
                      <View
                        style={[styles.dot, { backgroundColor: '#C2B90A' }]}
                      />
                      <Text style={styles.macroLabel}>Fats</Text>
                    </View>
                    <Text style={styles.macroValue}>
                      {editableNutrition.fats}g
                    </Text>
                  </View>
                </View>
              </View>{' '}
              {/* This closes the calories & macros box */}
              {/* Ingredients section - this should be a separate box */}
              <View style={styles.box}>
                <Text
                  style={[fontStyles.Maison_600_18PX_24LH, { marginBottom: 8 }]}
                >
                  Ingredients
                </Text>

                {editableIngredients?.map((item, index) => (
                  <View
                    key={index}
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginBottom: 12,
                    }}
                  >
                    <Text style={fontStyles.Maison_500_16PX_22LH}>
                      {item.name}
                    </Text>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        gap: 7,
                      }}
                    >
                      <TouchableOpacity
                        onPress={() => setEditingIngredientIndex(index)}
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          paddingHorizontal: 14,
                          paddingVertical: 8,
                          borderWidth: 1,
                          borderColor:
                            editingIngredientIndex === index
                              ? '#FF3F1F'
                              : '#E5E5E6',
                          borderRadius: 24,
                          backgroundColor: '#fff',
                          minWidth: 60,
                        }}
                      >
                        {editingIngredientIndex === index ? (
                          <TextInput
                            value={item.quantity.toString()}
                            onChangeText={(text) =>
                              updateIngredientWeight(index, text)
                            }
                            onBlur={() => setEditingIngredientIndex(null)}
                            keyboardType="numeric"
                            autoFocus
                            style={[
                              fontStyles.Maison_500_16PX_22LH,
                              {
                                textAlign: 'center',
                                minWidth: 30,
                                padding: 0,
                                margin: 0,
                              },
                            ]}
                          />
                        ) : (
                          <Text
                            style={[
                              fontStyles.Maison_500_16PX_22LH,
                              { marginRight: 2 },
                            ]}
                          >
                            {item.quantity}
                          </Text>
                        )}
                      </TouchableOpacity>
                      <Text
                        style={[
                          fontStyles.Maison_400_16PX_22LH,
                          { color: '#666' },
                        ]}
                      >
                        g
                      </Text>
                      <TouchableOpacity onPress={() => removeIngredient(index)}>
                        <Minuscircle height={16} width={16} />
                      </TouchableOpacity>
                    </View>
                  </View>
                ))}

                {/* Add new ingredient section */}
                {isAddingIngredient ? (
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginBottom: 12,
                      paddingVertical: 4,
                    }}
                  >
                    <TextInput
                      placeholder="Ingredient name"
                      value={newIngredientName}
                      onChangeText={setNewIngredientName}
                      style={[
                        fontStyles.Maison_500_16PX_22LH,
                        {
                          flex: 1,
                          paddingVertical: 2,
                          paddingHorizontal: 12,
                          borderWidth: 1,
                          borderColor: '#E5E5E6',
                          borderRadius: 20,
                          backgroundColor: '#fff',
                          marginRight: 12,
                        },
                      ]}
                    />
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        gap: 7,
                      }}
                    >
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          paddingHorizontal: 14,
                          paddingVertical: 8,
                          borderWidth: 1,
                          borderColor: '#E5E5E6',
                          borderRadius: 24,
                          backgroundColor: '#fff',
                          minWidth: 60,
                        }}
                      >
                        <TextInput
                          placeholder="0"
                          value={newIngredientWeight}
                          onChangeText={setNewIngredientWeight}
                          keyboardType="numeric"
                          style={[
                            fontStyles.Maison_500_16PX_22LH,
                            {
                              textAlign: 'center',
                              minWidth: 30,
                              padding: 0,
                              margin: 0,
                            },
                          ]}
                        />
                      </View>
                      <Text
                        style={[
                          fontStyles.Maison_400_16PX_22LH,
                          { color: '#666' },
                        ]}
                      >
                        g
                      </Text>
                      <TouchableOpacity
                        onPress={addNewIngredient}
                        style={{
                          backgroundColor: '#FF3F1F',
                          borderRadius: 12,
                          paddingHorizontal: 12,
                          paddingVertical: 6,
                        }}
                      >
                        <Text
                          style={{
                            color: 'white',
                            fontSize: 12,
                            fontWeight: '600',
                          }}
                        >
                          Add
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        onPress={() => {
                          setIsAddingIngredient(false);
                          setNewIngredientName('');
                          setNewIngredientWeight('');
                        }}
                      >
                        <Minuscircle height={16} width={16} />
                      </TouchableOpacity>
                    </View>
                  </View>
                ) : (
                  <TouchableOpacity
                    onPress={() => setIsAddingIngredient(true)}
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'left',
                      alignItems: 'center',
                      marginTop: 8,
                      paddingVertical: 10,
                      borderWidth: 1,
                      borderColor: '#fff',
                      borderRadius: 20,
                      borderStyle: 'dashed',
                      backgroundColor: '#fff',
                    }}
                  >
                    <Text
                      style={[
                        fontStyles.Maison_500_16PX_22LH,
                        { color: '#FF3F1F' },
                      ]}
                    >
                      + Add Ingredient
                    </Text>
                  </TouchableOpacity>
                )}
              </View>{' '}
              {/* This closes the ingredients box */}
            </View>
          </ScrollView>

          {/* Bottom section with Log meal button */}
          <View style={{ paddingHorizontal: 22 }}>
            <PrimaryBtn text="Log meal" onPress={handleLogMeal} />
          </View>

          <Calorieandmacro
            open={iscaloriesheet}
            initialValues={{
              calories: editableNutrition.calories.toString(),
              protein: editableNutrition.protein.toString(),
              carbs: editableNutrition.carbs.toString(),
              fat: editableNutrition.fats.toString(),
            }}
            onClose={(values) => {
              setiscaloriesheet(false);
              if (values) {
                // Validate the values before updating
                const newNutrition = {
                  calories: Math.max(0, parseFloat(values.calories) || 0),
                  protein: Math.max(0, parseFloat(values.protein) || 0),
                  carbs: Math.max(0, parseFloat(values.carbs) || 0),
                  fats: Math.max(0, parseFloat(values.fat) || 0),
                };
                setEditableNutrition(newNutrition);
                console.log('Updated nutrition values:', newNutrition);
              }
            }}
          />
          <Selectmealtype
            open={ismealtype}
            selectedMealType={selectedMealType}
            onClose={(mealType) => {
              setismealtype(false);
              if (mealType) {
                setSelectedMealType(mealType);
              }
            }}
          />

          <Dateandtime
            open={isdateTime}
            initialDate={selectedDateTime}
            onClose={(date) => {
              setisdateTime(false);
              if (date) {
                setSelectedDateTime(date);
              }
            }}
          />
        </>
      ) : (
        <View style={styles.container}>
          <View style={styles.imageWrapper}>
            <Image
              source={
                !photoUri
                  ? (fromTextAnalysis ? FoodScan2 : FoodScan)
                  : { uri: photoUri }
              }
              style={styles.image}
            />
            <Animated.View
              style={[
                styles.scanLine,
                {
                  transform: [{ translateY }],
                  opacity: scanAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0.2, 1],
                  }),
                },
              ]}
            />
          </View>

          <View style={styles.scancontainer}>
            <Star height={24} width={24} />
            <Text style={[fontStyles.Maison_600_20PX_28LH, styles.scanText]}>
              {fromTextAnalysis ? 'Analyzing your meal...' : 'Scanning Meal...'}
            </Text>
          </View>
          <View style={{ marginTop: 5 }}>
            <Text style={[fontStyles.Maison_500_14PX_18LH, styles.analyzingText]}>
              {fromTextAnalysis
                ? "We're estimating the calories, protein, carbs, and fat from your input."
                : 'Analyzing your meal with AI...'
              }
            </Text>
          </View>
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9F4ED',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 24,
  },
  scrollContainer: {
    paddingBottom: 20,
    backgroundColor: '#F9F4ED',
  },
  headerDivider: {
    height: 0.1,
    backgroundColor: '#00000005',
    width: '100%',
    marginTop: 1.5,
  },
  text: {
    fontSize: 18,
    fontWeight: '500',
    marginBottom: 12,
  },
  kcal: {
    marginTop: 10,
    marginLeft: 2,
  },
  scancontainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    marginTop: 50,
  },
  calorieText: {
    color: '#FF3F1F',
  },
  box: {
    backgroundColor: '#fff',
    borderRadius: 20,
    marginVertical: 12,
    padding: 16,
    width: '100%',
    gap: 16,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
    paddingHorizontal: 5,
  },
  calorieContainer: {
    flexDirection: 'row',
    gap: 7,
  },
  timeBox: {
    backgroundColor: 'white',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    borderColor: '#E5E5E6',
    borderWidth: 1,
  },
  timeText: {
    fontSize: 14,
    fontWeight: '500',
  },
  mealTypeBox: {
    backgroundColor: '#f4f4f4',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  mealTypeText: {
    fontSize: 14,
    fontWeight: '500',
  },
  imageBox: {
    alignItems: 'center',
    marginVertical: 12,
  },
  image: {
    width: 160,
    height: 160,
    borderRadius: 80,
    resizeMode: 'cover',
  },
  editButton: {
    color: '#FF3F1F',
  },
  dishName: {
    textAlign: 'center',
    justifyContent: 'center',
  },
  subTitle: {
    color: '#999',
    textAlign: 'center',
    marginTop: 2,
  },

  macroBarContainer: {
    flexDirection: 'row',
    height: 10,
    borderRadius: 5,
    overflow: 'hidden',
    marginVertical: 10,
  },

  macroSegment: {
    height: '100%',
  },

  macroLegendRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },

  macroLegendColumn: {
    alignItems: 'center',
    flex: 1,
  },

  macroLegendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },

  labelRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },

  dot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: 4,
  },

  macroLabel: {
    fontSize: 14,
    color: '#555',
  },

  macroValue: {
    marginTop: 6,
    fontSize: 14,
    fontWeight: '600',
  },

  imageWrapper: {
    position: 'relative',
  },
  scanLine: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 4,
    backgroundColor: 'rgba(0,255,0,0.5)',
    borderRadius: 2,
  },
  scanText: {
    fontSize: 20,
    fontWeight: '600',
    marginLeft: 8,
    justifyContent: 'center',
    // lineHeight: 28,
  },
  analyzingText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#888',
    justifyContent: 'center',
    marginLeft: 5,
    marginRight: 5,
    // lineHeight: 18,
  },

  // Add these styles to the StyleSheet
  textAnalysisPlaceholder: {
    width: 160,
    height: 160,
    borderRadius: 80,
    backgroundColor: '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    borderWidth: 2,
    borderColor: '#E5E5E6',
    borderStyle: 'dashed',
  },
  textAnalysisLabel: {
    color: '#666',
    textAlign: 'center',
    marginBottom: 8,
  },
  textDescription: {
    color: '#333',
    textAlign: 'center',
    fontSize: 12,
    lineHeight: 16,
  },
});

export default Mealdata;
