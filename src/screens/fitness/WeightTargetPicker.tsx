import { useFormik } from 'formik';
import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { StyleSheet, Text, TextInput, View } from 'react-native';
import * as Yup from 'yup';
import { PrimaryBtn } from '../../components/Buttons/Btns';

export type WeightAdjustmentInputRef = {
  submit: () => void;
};

type Props = {
  onSubmit: (data: { value: number; unit: string }) => void;
  initialValue?: { value: number; unit: string; type: '+' | '-' };
};

export const WeightAdjustmentInput = forwardRef<
  WeightAdjustmentInputRef,
  Props
>(({ onSubmit, initialValue }, ref) => {
  const [adjustType, setAdjustType] = useState<'+' | '-'>(
    initialValue?.type || '+',
  );
  const [unit, setUnit] = useState(initialValue?.unit || 'kg');

  const formik = useFormik({
    initialValues: {
      weight: initialValue?.value?.toString() || '',
    },
    validationSchema: Yup.object().shape({
      weight: Yup.number()
        .typeError('Enter a number')
        .required('Required')
        .positive('Must be greater than 0')
        .max(999, 'Too large'),
    }),
    onSubmit: (values) => {
      const adjustedValue =
        adjustType === '+'
          ? Math.abs(Number(values.weight))
          : -Math.abs(Number(values.weight));
      onSubmit({ value: adjustedValue, unit });
    },
  });

  useImperativeHandle(ref, () => ({
    submit: formik.handleSubmit,
  }));

  return (
    <View style={styles.container}>
      <Text style={styles.label}>Adjust Weight</Text>

      <View style={styles.row}>
        <PrimaryBtn
          text="+"
          onPress={() => setAdjustType('+')}
          style={[
            styles.adjustBtn,
            {
              backgroundColor: adjustType === '+' ? '#198754' : '#fff',
              borderColor: '#198754',
            },
          ]}
          textStyle={{ color: adjustType === '+' ? '#fff' : '#198754' }}
        />
        <PrimaryBtn
          text="-"
          onPress={() => setAdjustType('-')}
          style={[
            styles.adjustBtn,
            {
              backgroundColor: adjustType === '-' ? '#dc3545' : '#fff',
              borderColor: '#dc3545',
            },
          ]}
          textStyle={{ color: adjustType === '-' ? '#fff' : '#dc3545' }}
        />
      </View>

      <View style={styles.inputWrapper}>
        <TextInput
          placeholder="Enter weight"
          placeholderTextColor="#555"
          keyboardType="numeric"
          value={formik.values.weight}
          onChangeText={formik.handleChange('weight')}
          onBlur={formik.handleBlur('weight')}
          style={styles.input}
        />
        {formik.touched.weight && formik.errors.weight && (
          <Text style={styles.error}>{formik.errors.weight}</Text>
        )}
      </View>

      <View style={styles.unitRow}>
        <PrimaryBtn
          text="kg"
          onPress={() => setUnit('kg')}
          style={[
            styles.unitBtn,
            { backgroundColor: unit === 'kg' ? '#043F12' : '#fff' },
          ]}
          textStyle={{ color: unit === 'kg' ? '#fff' : '#043F12' }}
        />
        <PrimaryBtn
          text="lbs"
          onPress={() => setUnit('lbs')}
          style={[
            styles.unitBtn,
            { backgroundColor: unit === 'lbs' ? '#043F12' : '#fff' },
          ]}
          textStyle={{ color: unit === 'lbs' ? '#fff' : '#043F12' }}
        />
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 8,
    width: '100%',
  },
  label: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#043F12',
    marginBottom: 8,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  adjustBtn: {
    paddingVertical: 8,
    paddingHorizontal: 20,
    borderRadius: 20,
    borderWidth: 1,
  },
  inputWrapper: {
    width: '100%',
    marginBottom: 12,
  },
  input: {
    borderBottomWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  unitRow: {
    flexDirection: 'row',
    gap: 12,
  },
  unitBtn: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#043F12',
    minWidth: 80,
  },
  error: {
    color: 'red',
    fontSize: 12,
    marginTop: 4,
  },
});
