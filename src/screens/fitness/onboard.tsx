import { Formik, FormikHelpers } from 'formik';
import moment from 'moment';
import React from 'react';
import {
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import * as Yup from 'yup';
import { FitnessScreenProps } from '../../../@types/navigation';
import { PrimaryBtn } from '../../components/Buttons/Btns';
import SelectInputField from '../../components/SelectInputField/SelectInputField';
import { TextInputField } from '../../components/TextInputField/TextInputField';
import { verifyUser } from '../../utils/fitnessFunctions';
import {
  removeItemFromAsyncStorage,
  showSuccessToast,
  useBackHandlerWithCallback,
} from '../../utils/functions';
import Dateandtime from './datetime';
import { getUserId, supabase } from './supabaseClient';
interface Props {
  visible: boolean;
  isNewUser?: boolean;
  onClose: () => void;
  onSubmit: (values: FormValues) => void;
}

export interface FormValues {
  daily_goals_calories_burned: number;
  daily_goals_steps: number;
  height: number;
  gender: 'Male' | 'Female';
  date_of_birth: string;
  target_weight: number;
  target_calorie: number;
  weight: number;
  sleep_start_time: Date;
  sleep_end_time: Date;
  target_intake_calorie: number;
  target_calorie_deficit: number;
}

const initialValues: FormValues = {
  daily_goals_calories_burned: 500,
  daily_goals_steps: 7000,
  height: 178,
  gender: 'Male',
  date_of_birth: '2000-01-01',
  target_weight: 65,
  target_calorie: 2250,
  weight: 85,
  target_intake_calorie: 2250,
  target_calorie_deficit: 500,
  sleep_start_time: new Date(),
  sleep_end_time: new Date(),
};
const validationSchema = Yup.object({
  daily_goals_calories_burned: Yup.number()
    .min(0, 'Calories burned must be at least 0')
    .max(2000, 'Calories burned seems too high')
    .required('Calories burned is required'),

  daily_goals_steps: Yup.number()
    .min(1000, 'Minimum 1000 steps required')
    .max(50000, 'Steps exceed realistic limit')
    .required('Steps goal is required'),

  height: Yup.number()
    .min(100, 'Minimum height must be 100 cm')
    .max(250, 'Height exceeds human range')
    .required('Height is required'),

  gender: Yup.string()
    .oneOf(
      ['Male', 'Female'],
      'Add a valid gender exactly as "Male" or "Female"',
    )
    .required('Gender is required'),

  date_of_birth: Yup.date()
    .max(
      moment().subtract(10, 'years').toDate(),
      'Must be at least 10 years old',
    )
    .min(
      moment().subtract(100, 'years').toDate(),
      'Date of birth is too far back ex. 2000',
    )
    .required('Date of birth is required'),

  target_weight: Yup.number()
    .min(30, 'Target weight must be at least 30 kg')
    .max(300, 'Target weight exceeds realistic limit')
    .required('Target weight is required'),

  target_calorie: Yup.number()
    .min(1000, 'Target calorie should be at least 1000 kcal')
    .max(5000, 'Target calorie exceeds realistic limit')
    .required('Target calorie is required'),

  weight: Yup.number()
    .min(30, 'Weight must be at least 30 kg')
    .max(300, 'Weight exceeds realistic limit')
    .required('Weight is required'),

  target_intake_calorie: Yup.number()
    .min(1000, 'Intake calorie should be at least 1000 kcal')
    .max(5000, 'Intake calorie exceeds realistic limit')
    .required('Target intake calorie is required'),

  target_calorie_deficit: Yup.number()
    .min(0, 'Calorie deficit must be at least 0')
    .max(2000, 'Calorie deficit is too high')
    .required('Calorie deficit is required'),
  sleep_start_time: Yup.date()
    .required('Sleep start time is required')
    .typeError('Invalid sleep start time'),

  sleep_end_time: Yup.date()
    .required('Sleep end time is required')
    .typeError('Invalid sleep end time')
    .test(
      'is-after-start',
      'Sleep end time must be after sleep start time',
      function (endTime) {
        const { sleep_start_time: startTime } = this.parent;

        if (!(startTime instanceof Date) || !(endTime instanceof Date))
          return true;

        const start = new Date(startTime);
        let end = new Date(endTime);

        // 🕛 if end time is before or equal start, treat as next day
        if (end <= start) {
          end.setDate(end.getDate() + 1);
        }

        return end > start;
      },
    ),
});
const OnBoard: React.FC<FitnessScreenProps<'OnBoard'>> = ({
  navigation,
  route,
}) => {
  const isNewUser = Boolean(route.params?.newUser);
  const colors = route?.params?.theme?.colors;
  const [isDatePickerVisible, setDatePickerVisibility] = React.useState<
    [string, boolean]
  >(['', false]);

  useBackHandlerWithCallback(() => {
    console.log('Please Fill All values');
  });

  const handleSubmit = async (
    values: FormValues,
    formikHelpers: FormikHelpers<FormValues>,
  ) => {
    // if (values) {
    //   return;
    // }

    const userId = await getUserId();

    const timezone = moment.tz.guess();
    // Insert default fitness profile data into 'user_fitness_profiles'
    const defaultFitnessData = {
      daily_goals_calories_burned: values?.daily_goals_calories_burned,
      daily_goals_steps: values?.daily_goals_steps,
      height: values?.height,
      gender: values?.gender,
      date_of_birth: values?.date_of_birth,
      target_weight: values?.target_weight,
      target_calorie: values?.target_calorie,
      target_intake_calorie: values?.target_intake_calorie,
      target_calorie_deficit: values?.target_calorie_deficit,
      sleep_start_time: values?.sleep_start_time,
      sleep_end_time: values?.sleep_end_time,
      created_at: new Date().toISOString(),
      occupation: 'Software Engineer / Tech Entrepreneur',
      fitness_level: 'Beginner',
      experience_years: 0,
      past_experience: 'Mostly sedentary with occasional walks',
      limitations: 'Mild lower back pain',
      goals_set: 'Yes',
      primary_goal: 'Fat loss',
      secondary_goals: 'Improve flexibility, posture, and sleep quality',
      preferred_exercises:
        'Walking, light mobility drills, bodyweight exercises, swimming',
      availability: '2–3 days/week, mornings preferred',
      duration: '45–60 minutes',
      pain_points: 'Consistency and diet adherence due to work schedule',
      activity_level: 'Slightly Active',
      timezone,
    };

    const { error: fitnessInsertError } = await supabase
      .from('user_fitness_profiles')
      .insert({ user_id: userId, ...defaultFitnessData });

    const { error: weightInsertError } = await supabase
      .from('user_weight')
      .insert({ user_id: userId, weight_kg: 85, weight_lb: 187.5 });

    if (fitnessInsertError) {
      console.error('Error inserting fitness profile:', fitnessInsertError);
      console.log(
        'error',
        `Error inserting fitness profile: ${fitnessInsertError.message}`,
        'Login',
      );

      return;
    }

    if (weightInsertError) {
      console.error('Error inserting fitness profile:', weightInsertError);
      console.log(
        'error',
        `Error inserting fitness profile: ${weightInsertError.message}`,
        'Login',
      );

      return;
    }
    await verifyUser(userId);

    const response = await fetch(
      'https://ai-coach.delicut.click/persona-generation',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          user_id: userId,
          user_data: { ...defaultFitnessData },
        }),
      },
    );

    await removeItemFromAsyncStorage('isNewUser')?.then(() => {
      formikHelpers.setSubmitting(false);
      navigation?.navigate('Home_', { tokenRefresher: 'tokenRefresh' });
      showSuccessToast('Your information has been submitted.');
    });
  };

  const openDatePicker = [
    'sleep_start_time',
    'sleep_end_time',
    'date_of_birth',
  ];
  return (
    <View style={{ flex: 1, backgroundColor: colors?.primary_cream }}>
      <View style={styles.modalContainer}>
        <Text style={styles.title}>On Board</Text>
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({
            handleChange,
            handleBlur,
            handleSubmit,
            values,
            setFieldValue,
            errors,
            touched,
            isSubmitting,
          }) => {
            return (
              <ScrollView>
                {Object.entries(values).map(([key, value]) => (
                  <View key={key} style={styles.field}>
                    {key === 'gender' ? (
                      <SelectInputField
                        value={value}
                        placeholder="Gender"
                        option={[
                          { label: 'Male', value: 'Male' },
                          { label: 'Female', value: 'Female' },
                        ]}
                        onChange={(newValue) =>
                          handleChange('gender')(newValue)
                        }
                        header={key.replace(/_/g, ' ')}
                        style={{ paddingVertical: 25 }}
                        touched={touched[key as keyof FormValues]}
                        error={(
                          errors[key as keyof FormValues] &&
                          touched[key as keyof FormValues] &&
                          errors[key as keyof FormValues]
                        )?.toString()}
                      />
                    ) : (
                      <TouchableOpacity
                        onPress={() => {
                          openDatePicker.includes(key)
                            ? setDatePickerVisibility([key, true])
                            : console.log('no');
                        }}
                      >
                        <TextInputField
                          value={
                            openDatePicker.includes(key)
                              ? moment(value).format(
                                  key === 'date_of_birth'
                                    ? 'DD-MM-YYYY'
                                    : 'hh:mm A',
                                )
                              : String(value)
                          }
                          onChangeText={handleChange(key)}
                          keyboardType={'number-pad'}
                          header={key.replace(/_/g, ' ')}
                          touched={touched[key as keyof FormValues]}
                          error={(
                            errors[key as keyof FormValues] &&
                            touched[key as keyof FormValues] &&
                            errors[key as keyof FormValues]
                          )?.toString()}
                          disabled={openDatePicker.includes(key)}
                        />
                      </TouchableOpacity>
                    )}
                  </View>
                ))}

                <View style={styles.buttonGroup}>
                  <PrimaryBtn
                    text="Submit"
                    disabled={isSubmitting}
                    onPress={handleSubmit}
                    style={{
                      marginTop: 12,
                      flex: 1,
                      marginHorizontal: 8,
                    }}
                    disabledColor={colors?.primary_grenade}
                    isLoading={isSubmitting}
                    textStyle={{
                      fontSize: 16,
                      lineHeight: 19,
                      fontWeight: '600',
                    }}
                  />
                </View>
                {values[isDatePickerVisible?.[0]] && (
                  <Dateandtime
                    key={isDatePickerVisible?.[0]}
                    initialDate={values[isDatePickerVisible?.[0]]}
                    open={isDatePickerVisible?.[1]}
                    title={
                      isDatePickerVisible?.[0] === 'date_of_birth'
                        ? 'Date Of Birth'
                        : 'Select Time'
                    }
                    mode={
                      isDatePickerVisible?.[0] === 'date_of_birth'
                        ? 'date'
                        : 'time'
                    }
                    onClose={(date) => {
                      if (!date) {
                        setDatePickerVisibility(['', false]);
                      } else {
                        setFieldValue(
                          isDatePickerVisible?.[0],
                          date.toISOString(),
                        );
                        setDatePickerVisibility(['', false]);
                      }
                    }}
                  />
                )}
              </ScrollView>
            );
          }}
        </Formik>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  button: { flex: 1 },
  modalContainer: {
    flex: 1,
    padding: 20,
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    maxHeight: '90%',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  field: {
    marginBottom: 10,
  },
  label: {
    fontWeight: '600',
    marginBottom: 4,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 8,
  },
  error: {
    fontSize: 12,
    color: 'red',
  },
  buttonGroup: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 20,
    flex: 1,
  },
});

export default OnBoard;
