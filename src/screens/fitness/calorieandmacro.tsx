import { useTheme } from '@react-navigation/native';
import { Formik } from 'formik';
import React from 'react';
import {
  StyleProp,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import * as Yup from 'yup';
import { GorhomBottomSheet } from '../../components/BottomSheet/BottomSheet';
import { borderRadiusStyles } from '../../theme/styles/globalStyles';

interface Props {
  open: boolean;
  onClose: (values: CalorieMacroValues | null) => void;
  initialValues?: {
    calories: string;
    protein: string;
    carbs: string;
    fat: string;
  };
}

interface CalorieMacroValues {
  calories: string;
  protein: string;
  carbs: string;
  fat: string;
}

const validationSchema = Yup.object().shape({
  calories: Yup.number()
    .min(0, 'Must be a positive number')
    .required('Calories is required'),
  protein: Yup.number()
    .min(0, 'Must be a positive number')
    .required('Protein is required'),
  carbs: Yup.number()
    .min(0, 'Must be a positive number')
    .required('Carbs is required'),
  fat: Yup.number()
    .min(0, 'Must be a positive number')
    .required('Fat is required'),
});

export const Calorieandmacro: React.FC<Props> = ({
  open,
  onClose,
  initialValues = {
    calories: '0',
    protein: '0',
    carbs: '0',
    fat: '0',
  },
}) => {
  const colors = useTheme();

  const wrapperStyle: StyleProp<ViewStyle> = [
    borderRadiusStyles.br32,
    {
      borderColor: 'gray',
    },
  ];

  const containerStyle: StyleProp<ViewStyle> = {
    flex: 1,
    padding: 0,
    height: 90,
    margin: 5,
  };

  const handleClose = (values: CalorieMacroValues | null) => {
    onClose(values);
  };

  return (
    <GorhomBottomSheet
      sheetOpen={open}
      title="Calories & macros"
      closeOnBackdrop
      closeOnPressBack
      sheetClose={() => handleClose(null)}
    >
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        enableReinitialize={true}
        onSubmit={(values) => handleClose(values)}
      >
        {({
          handleChange,
          handleSubmit,
          values,
          errors,
          touched,
          isValid,
          setFieldValue,
        }) => (
          <View style={styles.container}>
            {/* Calories Input */}
            <View style={styles.inputContainer}>
              <View style={styles.labelRow}>
                <Text style={styles.inputLabel}>Calories (kcal)</Text>
              </View>
              <TextInput
                keyboardType="numeric"
                placeholder="Add Calories"
                value={values.calories}
                onChangeText={(text) => {
                  console.log('Calories changed to:', text);
                  handleChange('calories')(text);
                }}
                style={styles.foodNameInput}
                placeholderTextColor="#999"
                textAlign="center"
              />
              {touched.calories && errors.calories && (
                <Text style={styles.errorText}>{errors.calories}</Text>
              )}
            </View>

            <View style={styles.macroContainer}>
              {/* Protein Input */}
              <View style={styles.macroInputContainer}>
                <View style={styles.labelRow}>
                  <View style={[styles.dot, { backgroundColor: '#EF9700' }]} />
                  <Text style={styles.inputLabel}>Protein (g)</Text>
                </View>
                <TextInput
                  keyboardType="numeric"
                  placeholder="0"
                  value={values.protein}
                  onChangeText={(text) => {
                    console.log('Protein changed to:', text);
                    handleChange('protein')(text);
                  }}
                  style={styles.macroInput}
                  placeholderTextColor="#999"
                  textAlign="center"
                />
                {touched.protein && errors.protein && (
                  <Text style={styles.errorText}>{errors.protein}</Text>
                )}
              </View>

              {/* Carbs Input */}
              <View style={styles.macroInputContainer}>
                <View style={styles.labelRow}>
                  <View style={[styles.dot, { backgroundColor: '#6B8E23' }]} />
                  <Text style={styles.inputLabel}>Carbs (g)</Text>
                </View>
                <TextInput
                  keyboardType="numeric"
                  placeholder="0"
                  value={values.carbs}
                  onChangeText={(text) => {
                    console.log('Carbs changed to:', text);
                    handleChange('carbs')(text);
                  }}
                  style={styles.macroInput}
                  placeholderTextColor="#999"
                  textAlign="center"
                />
                {touched.carbs && errors.carbs && (
                  <Text style={styles.errorText}>{errors.carbs}</Text>
                )}
              </View>

              {/* Fat Input */}
              <View style={styles.macroInputContainer}>
                <View style={styles.labelRow}>
                  <View style={[styles.dot, { backgroundColor: '#C4AD3A' }]} />
                  <Text style={styles.inputLabel}>Fat (g)</Text>
                </View>
                <TextInput
                  keyboardType="numeric"
                  placeholder="0"
                  value={values.fat}
                  onChangeText={(text) => {
                    console.log('Fat changed to:', text);
                    handleChange('fat')(text);
                  }}
                  style={styles.macroInput}
                  placeholderTextColor="#999"
                  textAlign="center"
                />
                {touched.fat && errors.fat && (
                  <Text style={styles.errorText}>{errors.fat}</Text>
                )}
              </View>
            </View>

            {/* Add Save and Cancel buttons */}
            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={() => handleClose(null)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.button,
                  styles.saveButton,
                  !isValid && styles.disabledButton,
                ]}
                onPress={() => handleSubmit()}
                disabled={!isValid}
              >
                <Text style={styles.saveButtonText}>Save</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </Formik>
    </GorhomBottomSheet>
  );
};

const styles = {
  container: {
    paddingHorizontal: 16,
    paddingVertical: 10,
  },
  inputContainer: {
    alignItems: 'center' as 'center',
    marginVertical: 8,
    marginBottom: 20,
  },
  macroContainer: {
    flexDirection: 'row' as 'row',
    justifyContent: 'space-between' as 'space-between',
    marginBottom: 20,
  },
  macroInputContainer: {
    flex: 1,
    marginHorizontal: 4,
    alignItems: 'center' as 'center',
  },
  labelRow: {
    flexDirection: 'row' as 'row',
    alignItems: 'center' as 'center',
    gap: 4,
    marginBottom: 8,
  },
  dot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: 4,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600' as '600',
    color: '#333',
  },
  foodNameInput: {
    width: 280,
    height: 55,
    paddingTop: 16,
    paddingRight: 20,
    paddingBottom: 16,
    paddingLeft: 20,
    borderWidth: 0.6,
    borderColor: '#E5E5E6',
    borderRadius: 70,
    textAlign: 'center' as 'center',
    backgroundColor: '#fff',
    color: '#000',
  },
  macroInput: {
    width: 90,
    height: 55,
    paddingTop: 16,
    paddingRight: 20,
    paddingBottom: 16,
    paddingLeft: 20,
    borderWidth: 0.6,
    borderColor: '#E5E5E6',
    borderRadius: 70,
    textAlign: 'center' as 'center',
    backgroundColor: '#fff',
    color: '#000',
  },
  errorText: {
    color: '#FF3333',
    fontSize: 12,
    marginTop: 4,
  },
  buttonContainer: {
    flexDirection: 'row' as 'row',
    justifyContent: 'space-between' as 'space-between',
    marginTop: 20,
    paddingHorizontal: 10,
    gap: 10,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 25,
    alignItems: 'center' as 'center',
    justifyContent: 'center' as 'center',
  },
  cancelButton: {
    backgroundColor: '#f0f0f0',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  saveButton: {
    backgroundColor: '#FF3F1F',
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '600' as '600',
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600' as '600',
  },
};
