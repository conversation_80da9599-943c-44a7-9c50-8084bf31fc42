import React, { useEffect, useRef, useState } from 'react';
import {
  Alert,
  Dimensions,
  Modal,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { ImagePickerResponse, launchImageLibrary, MediaType } from 'react-native-image-picker';
import AntDesign from 'react-native-vector-icons/AntDesign';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {
  Camera,
  useCameraDevice,
  useCameraPermission,
} from 'react-native-vision-camera';
import Gallery from '../../assets/images/svgs/gallery.svg';
import { fontStyles } from '../../theme/fonts';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface Props {
  open: boolean;
  onClose: () => void;
  onImageSelected: (imageUri: string) => void;
}

const SimpleCameraComponent: React.FC<Props> = ({ open, onClose, onImageSelected }) => {
  const cameraRef = useRef<Camera>(null);
  const { hasPermission, requestPermission } = useCameraPermission();
  const device = useCameraDevice('back');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (open && !hasPermission) {
      requestPermission();
    }
  }, [open, hasPermission]);

  const handleTakePicture = async () => {

    console.log('📸 Taking picture...');
    setIsLoading(true);

    if (!cameraRef.current) {
      console.warn('Camera not ready');
      setIsLoading(false);
      return;
    }

    try {
      const photo = await cameraRef.current.takePhoto({
        flash: 'off',
        enableShutterSound: true,
      });

      console.log('📸 Photo taken:', photo);

      const imageUri = `file://${photo.path}`;

      // Call the callback with the image URI
      onImageSelected(imageUri);

      // Close the camera modal
      onClose();

    } catch (error) {
      console.error('Failed to take picture:', error);
      Alert.alert('Error', 'Failed to take picture. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenGallery = () => {
    console.log('📱 Opening gallery...');

    const options = {
      mediaType: 'photo' as MediaType,
      includeBase64: false,
      maxHeight: 2000,
      maxWidth: 2000,
      quality: 0.8,
    };

    launchImageLibrary(options, (response: ImagePickerResponse) => {
      console.log('Gallery response:', response);

      if (response.didCancel) {
        console.log('User cancelled gallery');
        return;
      }

      if (response.errorMessage) {
        console.error('Gallery error:', response.errorMessage);
        Alert.alert('Error', 'Failed to open gallery. Please try again.');
        return;
      }

      if (response.assets && response.assets[0] && response.assets[0].uri) {
        const imageUri = response.assets[0].uri;
        console.log('Selected image URI:', imageUri);

        // Call the callback with the selected image URI
        onImageSelected(imageUri);

        // Close the camera modal
        onClose();
      }
    });
  };

  const handleClose = () => {
    console.log('🚪 Closing camera----');
    onClose();
  };

  if (!open) {
    return null;
  }

  const isCameraReady = hasPermission && device;

  return (
    <Modal
      visible={open}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={handleClose}
    >
      <StatusBar hidden />
      <View style={styles.container}>
        {isCameraReady ? (
          <Camera
            ref={cameraRef}
            style={[StyleSheet.absoluteFill,{overflow:'visible'}]}
            device={device}
            isActive={open}
            photo={true}
          />
        ) : (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>
              {!hasPermission ? 'Camera permission required' : 'Loading camera...'}
            </Text>
            {!hasPermission && (
              <TouchableOpacity
                style={styles.permissionButton}
                onPress={requestPermission}
              >
                <Text style={styles.permissionButtonText}>Grant Permission</Text>
              </TouchableOpacity>
            )}
          </View>
        )}

        {/* Top Bar */}
        <View style={styles.topBar}>
          <View style={styles.topBarContent}>
            <TouchableOpacity
              onPress={() => Alert.alert('Info', 'Position your meal within the circle for best results')}
              style={styles.topBarButton}
            >
              <MaterialIcons name="info-outline" size={22} color="white" />
            </TouchableOpacity>

            <Text style={[fontStyles?.Maison_400_14PX_18LH, styles.topBarText]}>
              Place your meal inside the circle
            </Text>

            <TouchableOpacity
              onPress={handleClose}
              style={styles.topBarButton}
            >
              <AntDesign name="close" size={20} color="white" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Circle Overlay - only show when camera is ready */}
        {isCameraReady && (
          <View style={styles.circleOverlay}>
            <View style={styles.circle} />
          </View>
        )}

        {/* Bottom Controls */}
        <View style={styles.bottomControls}>
          <View style={styles.controlsRow}>
            {/* Gallery Button */}
            <View style={styles.controlColumn}>
              <TouchableOpacity
                onPress={handleOpenGallery}
                style={styles.controlItem}
                activeOpacity={0.7}
              >
                <View style={styles.galleryIconContainer}>
                  <Gallery height={24} width={20} />
                </View>
                <Text style={[styles.controlText, fontStyles?.Maison_500_12PX_16LH]}>
                  Gallery
                </Text>
              </TouchableOpacity>
            </View>

            {/* Camera Shutter Button */}
            <View style={styles.controlColumn}>
              <TouchableOpacity
                onPress={handleTakePicture}
                style={[
                  styles.shutterOuter,
                  isLoading && styles.shutterDisabled
                ]}
                activeOpacity={0.2}
              >
                <View style={[
                  styles.shutterInner,
                  isLoading && styles.shutterInnerDisabled
                ]} />
              </TouchableOpacity>
            </View>

            {/* Empty space for symmetry */}
            <View style={styles.controlColumn} />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'black',
    paddingHorizontal: 20,
  },
  loadingText: {
    color: 'white',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  permissionButton: {
    backgroundColor: '#FF3F1F',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  topBar: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
    paddingTop: 50, // Account for status bar
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  topBarContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  topBarButton: {
    padding: 5,
  },
  topBarText: {
    color: 'white',
    fontSize: 14,
    textAlign: 'center',
    flex: 1,
    marginHorizontal: 10,
  },
  circleOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 5,
  },
  circle: {
    width: Math.min(screenWidth * 0.8, 320),
    height: Math.min(screenWidth * 0.8, 320),
    borderRadius: Math.min(screenWidth * 0.4, 160),
    borderWidth: 2,
    borderColor: 'rgba(255,255,255,0.8)',
    borderStyle: 'dashed',
  },
  bottomControls: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    zIndex:1000,
    right: 0,
    backgroundColor: 'rgba(0,0,0,0.8)',
    paddingBottom: 40,
  },
  controlsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 30,
    paddingHorizontal: 20,
  },
  controlColumn: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  controlItem: {
    alignItems: 'center',
    padding: 10,
  },
  galleryIconContainer: {
    padding: 5,
  },
  controlText: {
    color: 'white',
    marginTop: 8,
    fontSize: 12,
  },
  shutterOuter: {
    width: 70,
    height: 70,
    borderRadius: 35,
    borderWidth: 3,
    borderColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  shutterInner: {
    width: 54,
    height: 54,
    borderRadius: 27,
    backgroundColor: 'white',
  },
  shutterDisabled: {
    borderColor: 'rgba(255,255,255,0.5)',
  },
  shutterInnerDisabled: {
    backgroundColor: 'rgba(255,255,255,0.5)',
  },
});

export default SimpleCameraComponent;
