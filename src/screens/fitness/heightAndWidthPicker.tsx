import { useFormik } from 'formik';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import { StyleSheet, Text, TextInput, View } from 'react-native';
import * as Yup from 'yup';
import { PrimaryBtn } from '../../components/Buttons/Btns';

type InputType = 'height' | 'weight';

export type HeightOrWeightInputRef = {
  submit: () => void;
};

export const HeightOrWeightInput = forwardRef<
  HeightOrWeightInputRef,
  {
    inputType: InputType;
    onSubmit: (data: { value: number; unit: string }) => void;
  }
>(({ inputType, onSubmit }, ref) => {
  const [unit, setUnit] = useState(inputType === 'height' ? 'cm' : 'kg');

  const formik = useFormik({
    initialValues: {
      main: '',
      ft: '',
      inch: '',
    },
    validationSchema:
      inputType === 'height'
        ? Yup.object().shape({
            main:
              unit === 'cm'
                ? Yup.number()
                    .typeError('Height must be a number')
                    .positive()
                    .required('Required')
                : Yup.mixed().notRequired(),
            ft:
              unit === 'ft-in'
                ? Yup.number()
                    .typeError('Feet must be a number')
                    .min(0)
                    .required('Required')
                : Yup.mixed().notRequired(),
            inch:
              unit === 'ft-in'
                ? Yup.number()
                    .typeError('Inches must be a number')
                    .min(0)
                    .max(11)
                : Yup.mixed().notRequired(),
          })
        : Yup.object().shape({
            main: Yup.number()
              .typeError('Weight must be a number')
              .positive()
              .required('Required'),
          }),
    onSubmit: (values) => {
      let finalValue = 0;
      if (inputType === 'height') {
        if (unit === 'cm') {
          finalValue = parseFloat(values.main);
        } else {
          const ft = parseInt(values.ft || '0', 10);
          const inch = parseInt(values.inch || '0', 10);
          finalValue = ft * 30.48 + inch * 2.54;
        }
      } else {
        finalValue =
          unit === 'kg'
            ? parseFloat(values.main)
            : parseFloat(values.main) * 0.453592;
      }
      onSubmit({
        value: parseFloat(finalValue.toFixed(2)),
        unit,
      });
    },
  });

  useImperativeHandle(ref, () => ({
    submit: formik.handleSubmit,
  }));

  useEffect(() => {
    formik.resetForm();
  }, [unit]);

  return (
    <View style={styles.container}>
      <Text style={styles.label}>
        {inputType === 'height' ? 'Height' : 'Weight'}
      </Text>

      {inputType === 'height' && unit === 'ft-in' ? (
        <View style={[styles.row, { gap: 12 }]}>
          <View style={styles.inputWrapper}>
            <TextInput
              placeholder="ft"
              placeholderTextColor="#555"
              keyboardType="numeric"
              value={formik.values.ft}
              onChangeText={formik.handleChange('ft')}
              onBlur={formik.handleBlur('ft')}
              style={styles.input}
            />
            {formik.touched.ft && formik.errors.ft && (
              <Text style={styles.error}>{formik.errors.ft}</Text>
            )}
          </View>
          <View style={styles.inputWrapper}>
            <TextInput
              placeholder="in"
              placeholderTextColor="#555"
              keyboardType="numeric"
              value={formik.values.inch}
              onChangeText={formik.handleChange('inch')}
              onBlur={formik.handleBlur('inch')}
              style={styles.input}
            />
            {formik.touched.inch && formik.errors.inch && (
              <Text style={styles.error}>{formik.errors.inch}</Text>
            )}
          </View>
        </View>
      ) : (
        <View style={[styles.inputWrapper, { marginBottom: 50 }]}>
          <TextInput
            placeholder={`Enter ${inputType} (${unit}) `}
            placeholderTextColor="#555"
            keyboardType="numeric"
            value={formik.values.main}
            onChangeText={formik.handleChange('main')}
            onBlur={formik.handleBlur('main')}
            style={[styles.input, { height: 50 }]}
          />
          {formik.touched.main && formik.errors.main && (
            <Text style={styles.error}>{formik.errors.main}</Text>
          )}
        </View>
      )}
      <View style={styles.unitRow}>
        {inputType === 'height' ? (
          <>
            <PrimaryBtn
              text="cm"
              onPress={() => setUnit('cm')}
              style={[
                styles.unitBtn,
                { backgroundColor: unit === 'cm' ? '#043F12' : '#fff' },
              ]}
              textStyle={{ color: unit === 'cm' ? '#fff' : '#043F12' }}
            />
            <PrimaryBtn
              text="ft/in"
              onPress={() => setUnit('ft-in')}
              style={[
                styles.unitBtn,
                { backgroundColor: unit === 'ft-in' ? '#043F12' : '#fff' },
              ]}
              textStyle={{ color: unit === 'ft-in' ? '#fff' : '#043F12' }}
            />
          </>
        ) : (
          <>
            <PrimaryBtn
              text="kg"
              onPress={() => setUnit('kg')}
              style={[
                styles.unitBtn,
                { backgroundColor: unit === 'kg' ? '#043F12' : '#fff' },
              ]}
              textStyle={{ color: unit === 'kg' ? '#fff' : '#043F12' }}
            />
            <PrimaryBtn
              text="lbs"
              onPress={() => setUnit('lbs')}
              style={[
                styles.unitBtn,
                { backgroundColor: unit === 'lbs' ? '#043F12' : '#fff' },
              ]}
              textStyle={{ color: unit === 'lbs' ? '#fff' : '#043F12' }}
            />
          </>
        )}
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    padding: 16,
    // backgroundColor: '#f5f5f5',
    borderRadius: 8,
    width: '100%',
  },
  label: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#043F12',
  },
  unitRow: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginVertical: 16,
    gap: 12,
  },
  unitBtn: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#043F12',
    minWidth: 80,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    // marginBottom: 16,
  },
  inputWrapper: {
    flex: 1,
    // marginBottom: 16,
  },
  input: {
    borderBottomWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  error: {
    color: 'red',
    fontSize: 12,
    marginTop: 4,
  },
});
