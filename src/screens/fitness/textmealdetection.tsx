import { NavigationProp, useNavigation } from '@react-navigation/native';
import { StackScreenProps } from '@react-navigation/stack';
import React, { useState } from 'react';
import {
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { useDispatch } from 'react-redux';
import { RootFitnessStackParamList } from '../../../@types/navigation';
import { PrimaryBtn } from '../../components/Buttons/Btns';
import { useUserId } from '../../components/FitnessComponent/hooks/useUserId';
import Header from '../../components/Header/Header';
import { textMealDetection } from '../../redux/action/appActions';
import { fontStyles } from '../../theme/fonts';
import { showErrorToast } from '../../utils/functions';

const TextMealDetection: React.FC<
  StackScreenProps<RootFitnessStackParamList, 'TextMealDetection'>
> = ({ route }) => {
  const { colors } = route?.params?.theme;
  const dispatch = useDispatch();
  const navigation = useNavigation<NavigationProp<RootFitnessStackParamList>>();
  const { loading: userFetchLoading, userId } = useUserId();

  // State management
  const [selectedMealType, setSelectedMealType] = useState<string>('breakfast');
  const [mealDescription, setMealDescription] = useState<string>('');

  const mealTypes = [
    { id: 'breakfast', label: 'Breakfast' },
    { id: 'meal', label: 'Meal' },
    { id: 'snack', label: 'Snack' },
  ];

  const handleMealTypeSelection = (mealType: string) => {
    setSelectedMealType(mealType);
  };

  const validateInput = (): boolean => {
    if (!mealDescription.trim()) {
      showErrorToast('Please describe what you ate');
      // ToastAndroid.show('Please describe what you ate', ToastAndroid.SHORT);
      return false;
    }

    // if (mealDescription.trim().length < 5) {
    //   showErrorToast('Please provide a more detailed description');
    //   // ToastAndroid.show(
    //   //   'Please provide a more detailed description',
    //   //   ToastAndroid.SHORT,
    //   // );
    //   return false;
    // }

    return true;
  };

  const handleDetectNutrition = () => {
    if (!validateInput()) {
      return;
    }

    if (!userId) {
      showErrorToast('User ID not found');
      // ToastAndroid.show('User ID not found', ToastAndroid.SHORT);
      return;
    }

    // First navigate to Mealdata with text analysis flags
    navigation.navigate('Mealdata', {
      photoUri: '', // No photo for text analysis
      fromTextAnalysis: true,
      textDescription: mealDescription.trim(),
      selectedMealType: selectedMealType,
      analysisResult: null, // Add this to match the expected parameters
    });

    // Dispatch the text meal detection action
    dispatch(
      textMealDetection({
        food_description: mealDescription.trim(),
        meal_type: selectedMealType,
        user_id: userId,
      }),
    );
  };

  return (
    <>
      <Header
        headerTitle="Log what you ate"
        showDivider={false}
        textStyle={{ textAlign: 'center', flex: 0.9 }}
      />
      <View style={styles.headerDivider} />
      <View style={{ flex: 1, backgroundColor: '#F9F4ED' }}>
        {/* <ScrollView contentContainerStyle={styles.scrollContainer}> */}
        <View style={styles.container}>
          {/* Meal Type Selection */}

          {/* Description Input */}
          <View style={styles.box}>
            <View style={styles.mealTypeContainer}>
              {mealTypes.map((mealType) => (
                <TouchableOpacity
                  key={mealType.id}
                  style={[
                    styles.mealTypeButton,
                    selectedMealType === mealType.id &&
                      styles.selectedMealTypeButton,
                  ]}
                  onPress={() => handleMealTypeSelection(mealType.id)}
                >
                  <Text
                    style={[
                      styles.mealTypeText,
                      selectedMealType === mealType.id &&
                        styles.selectedMealTypeText,
                    ]}
                  >
                    {mealType.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <Text
              style={[fontStyles.Maison_600_18PX_24LH, styles.sectionTitle]}
            >
              What did you have?
            </Text>

            <Text style={[fontStyles.Maison_400_12PX_16LH, styles.subtitle]}>
              Describe what you ate. We'll estimate the calories and macros for
              you.
            </Text>

            <TextInput
              style={[fontStyles.Maison_600_18PX_24LH, styles.textInput]}
              placeholder="Start typing..."
              placeholderTextColor="#95989D"
              multiline
              numberOfLines={25}
              textAlignVertical="top"
              value={mealDescription}
              onChangeText={setMealDescription}
              maxLength={500}
            />
          </View>
        </View>
        {/* </ScrollView> */}

        {/* Bottom Button */}
        <View style={styles.bottomContainer}>
          <PrimaryBtn
            text="Detect nutrition"
            disabledColor={colors?.primary_grenade}
            onPress={handleDetectNutrition}
            disabled={!mealDescription.trim()}
          />
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9F4ED',
    paddingHorizontal: 24,
    paddingTop: 16,
  },
  scrollContainer: {
    paddingBottom: 20,
    backgroundColor: '#F9F4ED',
  },
  headerDivider: {
    height: 0.1,
    backgroundColor: '#00000005',
    width: '100%',
    marginTop: 1.5,
  },
  box: {
    backgroundColor: '#fff',
    borderRadius: 20,
    // marginVertical: 12,
    flex: 1,
    padding: 10,
    width: '100%',
  },
  mealTypeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
    marginVertical: 10,
  },
  mealTypeButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 12,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#E5E5E6',
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
  },
  selectedMealTypeButton: {
    backgroundColor: '#043F12',
    borderColor: '#043F12',
  },
  mealTypeText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  selectedMealTypeText: {
    color: '#fff',
  },
  sectionTitle: {
    marginVertical: 8,
    marginTop: 15,
    color: '#121212',
  },
  subtitle: {
    color: '#62656A',
    marginBottom: 16,
    lineHeight: 20,
  },
  textInput: {
    fontSize: 16,
    flex: 1,
    lineHeight: 22,
    fontFamily: 'Maison-Regular',
    color: '#333',
    minHeight: 120,
    fontWeight: 'bold',
  },
  characterCountContainer: {
    alignItems: 'flex-end',
    marginTop: 8,
  },
  characterCount: {
    fontSize: 12,
    color: '#999',
    fontFamily: 'Maison-Regular',
  },
  bottomContainer: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    marginTop: 20,
    backgroundColor: '#fff',
    borderTopRightRadius: 30,
    borderTopLeftRadius: 30,
  },
});

export default TextMealDetection;
