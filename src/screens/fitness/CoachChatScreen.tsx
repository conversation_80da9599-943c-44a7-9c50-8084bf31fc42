import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Al<PERSON>,
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { Loader } from '../../assets/images';
import CoachChatUI from '../../components/FitnessComponent/CoachChatUI';
import { initializeOptionsApi } from '../../utils/api';
import { supabase } from './supabaseClient';

interface Message {
  id: string;
  sender: 'user' | 'coach';
  text: string;
  timestamp: string;
  type?: 'text' | 'metric' | 'action';
  options?: string[];
  metrics?: {
    water: { current: number; target: number };
    meals: number;
    steps: { current: number; target: number };
    sleep: string;
  };
  delivered?: boolean;
}

const CoachChatScreen = ({ navigation, route }: any) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [userData, setUserData] = useState<any>(null);
  const [coachData, setCoachData] = useState<any>(null);
  const [isChatLoading, setIsChatLoading] = useState(true);
  const [coachPersonas, setCoachPersonas] = useState<any[]>([]);
  const [userHasCoach, setUserHasCoach] = useState<boolean | null>(null);
  const [showCoachSelection, setShowCoachSelection] = useState(false);

  // Sample data - replace with actual data from your API

  useEffect(() => {
    fetchUserData();
    // Initialize with sample messages
  }, []);

  useEffect(() => {
    if (userData?.user_id) {
      checkUserHasCoach(userData.user_id);
    }
  }, [userData]);

  useEffect(() => {
    if (userHasCoach === true && userData?.user_id) {
      fetchCoachData();
      fetchChatHistory(userData.user_id);
    } else if (userHasCoach === false) {
      fetchCoachPersonas();
      setShowCoachSelection(true);
      setIsChatLoading(false);
    }
  }, [userHasCoach, userData]);

  const fetchUserData = async () => {
    try {
      const {
        data: { user },
        error: userError,
      } = await supabase.auth.getUser();

      if (userError || !user) return;

      const { data, error } = await supabase
        .from('user_fitness_profiles')
        .select('user_id')
        .eq('user_id', user.id)
        .single();

      if (!error) setUserData(data);
    } catch (err) {
      console.error('Unexpected error:', err);
    }
  };

  const checkUserHasCoach = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('user_coach_map')
        .select('user_id, coach_id')
        .eq('user_id', userId);

      if (!error) {
        const userExists = data && data.length > 0;
        setUserHasCoach(userExists);
      } else {
        setUserHasCoach(false);
      }
    } catch (err) {
      console.error('Error checking user coach:', err);
      setUserHasCoach(false);
    }
  };

  const fetchCoachPersonas = async () => {
    try {
      const { data, error } = await supabase
        .from('coach_persona')
        .select('id, persona_name');

      if (!error) {
        setCoachPersonas(data || []);
      }
    } catch (err) {
      console.error('Error fetching coach personas:', err);
    }
  };

  const fetchCoachData = async () => {
    if (!userData?.user_id) return;

    try {
      const { data, error } = await supabase
        .from('user_coach_map')
        .select(
          `
          coach_id,
          coach_persona (
            id,
            persona_name
          )
        `,
        )
        .eq('user_id', userData.user_id)
        .single();

      if (!error && data) {
        setCoachData(data.coach_persona);
      }
    } catch (err) {
      console.error('Error fetching coach data:', err);
    }
  };

  const fetchChatHistory = async (userId: string) => {
    try {
      setIsChatLoading(true);
      const res = await fetch(
        `https://ai-coach.delicut.click/chat_history?user_id=${userId}&page=1`,
      );
      const json = await res.json();

      console.log('formatted', json);
      if (json?.messages) {
        const formatted = json.messages.map((msg: any, index: number) => ({
          id: `history_${index}`,
          sender: msg.role === 'ai' ? 'coach' : 'user',
          text: msg.message,
          timestamp: msg.created_at || new Date().toISOString(),
          delivered: msg.role === 'user',
        }));

        // Set the formatted messages

        setMessages(formatted);

        // After setting messages, fetch current options
        await fetchCurrentOptions(userId, formatted);
      } else {
        // If no messages in history, still check for options
        await fetchCurrentOptions(userId, []);
      }
    } catch (err) {
      console.error('Failed to load chat history:', err);
      // Even if chat history fails, try to fetch options
      await fetchCurrentOptions(userId, []);
    } finally {
      setIsChatLoading(false);
    }
  };

  // Fetch current available options for the user and add them to the latest coach message
  // This ensures options are available even after returning to the chat
  const fetchCurrentOptions = async (
    userId: string,
    currentMessages: Message[],
  ) => {
    try {
      console.log('Fetching current options for user:', userId);
      const response = await initializeOptionsApi(userId);
      const json = response.data;

      console.log('Options response:', json);

      // Handle both possible response formats: direct array or nested in options property
      const options = Array.isArray(json) ? json : json?.options;

      if (options && options.length > 0) {
        // Find the latest coach message
        const latestCoachMessageIndex = currentMessages
          .map((msg, index) => ({ msg, index }))
          .filter(({ msg }) => msg.sender === 'coach')
          .pop()?.index;

        if (latestCoachMessageIndex !== undefined) {
          // Add options to the latest coach message
          console.log(
            'Adding options to latest coach message at index:',
            latestCoachMessageIndex,
          );
          const updatedMessages = [...currentMessages];
          updatedMessages[latestCoachMessageIndex] = {
            ...updatedMessages[latestCoachMessageIndex],
            options: options,
          };
          setMessages(updatedMessages);
        } else {
          // If no coach messages exist or no messages at all, create a new one with options
          console.log('Creating new message with options');
          // const optionsMessage: Message = {
          //   id: `options_${Date.now()}`,
          //   sender: 'coach',
          //   text: 'What would you like to do?',
          //   timestamp: new Date().toISOString(),
          //   type: 'text',
          //   options: options,
          // };
          // setMessages([...currentMessages, optionsMessage]);
        }
      } else {
        console.log('No options available or empty options array');
      }
    } catch (err) {
      console.error('Failed to fetch current options:', err);
    }
  };

  const handleSendMessage = async (message: string) => {
    if (!userData?.user_id) {
      Alert.alert('Error', 'User data not available');
      return;
    }

    // Add user message immediately
    const userMessage: Message = {
      id: `user_${Date.now()}`,
      sender: 'user',
      text: message,
      timestamp: new Date().toISOString(),
      delivered: false,
    };

    // Clear options from all previous messages before adding new user message
    setMessages((prev) => [
      ...prev.map((msg) => ({ ...msg, options: undefined })),
      userMessage,
    ]);
    setIsLoading(true);

    try {
      const res = await fetch('https://ai-coach.delicut.click/ai_coach', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          user_id: userData.user_id,
          query: message,
        }),
      });

      const json = await res.json();

      // Mark user message as delivered
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === userMessage.id ? { ...msg, delivered: true } : msg,
        ),
      );

      if (json?.response) {
        const coachMessage: Message = {
          id: `coach_${Date.now()}`,
          sender: 'coach',
          text: json.response.content,
          timestamp: new Date().toISOString(),
          type: 'text',
          options: json.response.options || [],
        };

        setMessages((prev) => [...prev, coachMessage]);
      }
    } catch (err) {
      console.error('AI coach error:', err);
      Alert.alert('Error', 'Failed to send message. Please try again.');

      // Remove user message on error
      setMessages((prev) => prev.filter((msg) => msg.id !== userMessage.id));
    } finally {
      setIsLoading(false);
    }
  };

  const handleOptionSelect = async (option: string) => {
    await handleSendMessage(option);
  };

  const handleCoachSelect = async (coachId: string) => {
    if (!userData?.user_id) return;

    setIsLoading(true);
    try {
      const res = await fetch('https://ai-coach.delicut.click/select_coach', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          user_id: userData.user_id,
          coach_id: coachId,
        }),
      });

      const json = await res.json();

      if (json?.content) {
        // Create initial coach message
        const initialMessage: Message = {
          id: `coach_${Date.now()}`,
          sender: 'coach',
          text: json.content,
          timestamp: new Date().toISOString(),
          type: 'text',
          options: json.options || [],
        };

        setMessages([initialMessage]);
        setShowCoachSelection(false);
        setUserHasCoach(true);

        // Fetch the coach data to update the UI
        await fetchCoachData();
      }
    } catch (err) {
      console.error('Error selecting coach:', err);
      Alert.alert('Error', 'Failed to select coach. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackPress = () => {
    navigation.goBack();
  };

  const coachName = coachData?.persona_name || 'Coach';

  if (isChatLoading) {
    return (
      <View style={styles.centeredMessage}>
        <Loader fontSize={100} />
        <Text style={{ marginTop: 8, color: 'black' }}>Loading Chat...</Text>
      </View>
    );
  }

  if (showCoachSelection) {
    return (
      <View style={styles.coachSelectionContainer}>
        {isLoading && (
          <View style={styles.loadingOverlay}>
            <ActivityIndicator size="large" color="#FF6B35" />
            <Text style={styles.loadingText}>Please wait...</Text>
          </View>
        )}

        <View style={styles.coachSelectionContent}>
          {userHasCoach === null ? (
            <View style={styles.centeredMessage}>
              <ActivityIndicator size="small" color="#FF6B35" />
              <Text style={styles.checkingText}>
                Checking coach assignment...
              </Text>
            </View>
          ) : !userHasCoach && coachPersonas.length === 0 ? (
            <View style={styles.centeredMessage}>
              <Text style={styles.noCoachText}>
                No coach profiles available.
              </Text>
            </View>
          ) : (
            <>
              <View style={styles.headerSection}>
                <TouchableOpacity
                  onPress={handleBackPress}
                  style={styles.backButton}
                >
                  <Text style={styles.backArrow}>←</Text>
                </TouchableOpacity>
                <Text style={styles.selectCoachTitle}>Select Your Coach</Text>
                <View style={styles.headerSpacer} />
              </View>

              <View style={styles.coachListContainer}>
                <Text style={styles.subtitle}>
                  Choose a coach that matches your fitness goals and personality
                </Text>
                <FlatList
                  data={coachPersonas}
                  keyExtractor={(item) => item.id}
                  contentContainerStyle={styles.coachList}
                  showsVerticalScrollIndicator={false}
                  renderItem={({ item }) => (
                    <TouchableOpacity
                      onPress={() => handleCoachSelect(item.id)}
                      style={styles.coachCard}
                      activeOpacity={0.8}
                    >
                      <View style={styles.coachCardContent}>
                        <View style={styles.coachAvatar}>
                          <Text style={styles.avatarText}>
                            {item.persona_name.charAt(0)}
                          </Text>
                        </View>
                        <View style={styles.coachInfo}>
                          <Text style={styles.coachName}>
                            {item.persona_name}
                          </Text>
                          <Text style={styles.coachDescription}>
                            Tap to start your fitness journey
                          </Text>
                        </View>
                        <View style={styles.selectArrow}>
                          <Text style={styles.arrowText}>→</Text>
                        </View>
                      </View>
                    </TouchableOpacity>
                  )}
                />
              </View>
            </>
          )}
        </View>
      </View>
    );
  }

  return (
    <CoachChatUI
      coachName={coachName}
      messages={messages}
      onSendMessage={handleSendMessage}
      onOptionSelect={handleOptionSelect}
      onBackPress={handleBackPress}
      inputValue={inputValue}
      setInputValue={setInputValue}
      isLoading={isLoading}
    />
  );
};

const styles = StyleSheet.create({
  centeredMessage: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FDF5EF',
  },
  coachSelectionContainer: {
    flex: 1,
    backgroundColor: '#FDF5EF',
  },
  coachSelectionContent: {
    flex: 1,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(253, 245, 239, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  loadingText: {
    marginTop: 8,
    color: '#333',
    fontWeight: '600',
    fontSize: 16,
  },
  checkingText: {
    marginTop: 8,
    color: '#666',
    fontSize: 16,
  },
  noCoachText: {
    color: '#666',
    fontSize: 16,
    textAlign: 'center',
  },
  headerSection: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FDF5EF',
    borderBottomWidth: 1,
    borderBottomColor: '#F0E6D6',
  },
  backButton: {
    padding: 8,
  },
  backArrow: {
    fontSize: 24,
    color: '#333',
    fontWeight: 'bold',
  },
  headerSpacer: {
    width: 32,
  },
  selectCoachTitle: {
    flex: 1,
    color: '#333',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginLeft: -32, // Compensate for back button to center the title
  },
  coachListContainer: {
    flex: 1,
    paddingTop: 20,
  },
  subtitle: {
    color: '#666',
    fontSize: 14,
    textAlign: 'center',
    marginHorizontal: 24,
    marginBottom: 24,
    lineHeight: 20,
  },
  coachList: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  coachCard: {
    backgroundColor: 'white',
    borderRadius: 18,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  coachCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  coachAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#FF6B35',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  avatarText: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  coachInfo: {
    flex: 1,
  },
  coachName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  coachDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
  },
  selectArrow: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  arrowText: {
    fontSize: 18,
    color: '#FF6B35',
    fontWeight: 'bold',
  },
});

export default CoachChatScreen;
