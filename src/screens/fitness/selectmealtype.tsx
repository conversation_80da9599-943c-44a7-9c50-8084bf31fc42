import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Success } from '../../assets/images';
import { GorhomBottomSheet } from '../../components/BottomSheet/BottomSheet';
import { fontStyles } from '../../theme/fonts';

interface Props {
  open: boolean;
  selectedMealType: string;
  onClose: (mealType: string | null) => void;
}

const mealTypes = [
  { id: 'breakfast', name: 'Breakfast' },
  { id: 'meal', name: 'Meal' },
  { id: 'snack', name: 'Snack' },
];

const Selectmealtype: React.FC<Props> = ({ open, selectedMealType, onClose }) => {
  const [tempSelection, setTempSelection] = React.useState(selectedMealType);

  const handleMealTypeSelect = (mealType: string) => {
    setTempSelection(mealType);
  };

  const handleConfirm = () => {
    onClose(tempSelection);
  };

  const handleClose = () => {
    onClose(null);
  };

  return (
    <GorhomBottomSheet
      sheetOpen={open}
      sheetClose={handleClose}
      title="Select meal type"
      closeOnBackdrop
      closeOnPressBack
    >
      <View style={styles.container}>
        <View style={styles.optionsContainer}>
          {mealTypes.map((meal) => (
            <TouchableOpacity
              key={meal.id}
              style={[
                styles.option,
                tempSelection === meal.id && styles.selectedOption
              ]}
              onPress={() => handleMealTypeSelect(meal.id)}
            >
              <View style={styles.iconContainer}>
                {tempSelection === meal.id && (
                  <Success width={20} height={20} />
                )}
              </View>
              <Text style={[
                fontStyles.Maison_500_16PX_22LH,
                styles.optionText
              ]}>
                {meal.name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <TouchableOpacity
          style={styles.confirmButton}
          onPress={handleConfirm}
        >
          <Text style={styles.confirmButtonText}>Confirm</Text>
        </TouchableOpacity>
      </View>
    </GorhomBottomSheet>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  optionsContainer: {
    marginBottom: 30,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    marginBottom: 8,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#FFFFFF',
    backgroundColor: '#FFFFFF',
  },
  selectedOption: {
    borderColor: '#FF3F1F',
  },
  iconContainer: {
    width: 20,
    height: 20,
    marginRight: 16,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderRadius: 14,
    borderColor: "#DCD5D7"
  },
  optionText: {
    flex: 1,
    color: '#333333',
  },
  confirmButton: {
    backgroundColor: '#FF3F1F',
    borderRadius: 25,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  confirmButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default Selectmealtype;
