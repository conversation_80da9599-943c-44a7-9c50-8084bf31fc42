import { NavigationProp, RouteProp } from '@react-navigation/native';
import { FormikProps, useFormik } from 'formik';
import React, { useEffect, useRef, useState } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import * as Yup from 'yup';
import {
  GuestScreenProps,
  RootGuestStackParamList,
} from '../../../../@types/navigation';
import {
  KcaConsumedIcon2,
  LeftArrow,
  Loader,
  Logo,
} from '../../../assets/images';
import { PrimaryBtn } from '../../../components/Buttons/Btns';
import { StepperDateInput } from '../../../components/TextInputField/DateInput';
import { fontStyles, RFont } from '../../../theme/fonts';
import AdjustmentCounter from '../../fitness/adjustmentCounter';
import {
  HeightOrWeightInput,
  HeightOrWeightInputRef,
} from '../../fitness/heightAndWidthPicker';

// Type definitions
type StepType =
  | 'staticText'
  | 'activitySelector'
  | 'dateInput'
  | 'genderSelector'
  | 'heightInput'
  | 'weightInput'
  | 'targetWeightInput'
  | 'targetStepsInput'
  | 'targetCaloriesInput'
  | 'settingUpData'
  | 'allDone';

type MeasurementUnit = {
  value: number;
  unit: string;
};

interface OnBoardingFormValues {
  activityLevel: string;
  birthdate: string | Date;
  gender: string;
  height: MeasurementUnit;
  weight: MeasurementUnit;
  targetWeight: MeasurementUnit;
  targetSteps: number;
  targetCalories: number;
}

interface StepConfig {
  title: string;
  btnTxt: string | null;
  subTitle?: string;
  type: StepType;
  validationSchema?: Yup.ObjectSchema<any>;
}

type StepsConfig = {
  [key: string | number]: StepConfig;
};

interface ActivitySelectorProps {
  value: string;
  onChange: (value: string) => void;
  error: string | undefined;
}

interface GenderSelectorProps {
  value: string;
  onChange: (value: string) => void;
  error: string | undefined;
}

interface RenderStepComponentProps {
  step: StepConfig;
  formik: FormikProps<OnBoardingFormValues>;
}

const style = StyleSheet.create({
  mainContainer: {
    backgroundColor: 'white',
    borderRadius: 20,
    flex: 10,
    margin: 16,
    padding: 15,
  },
  selectedText: {
    backgroundColor: '#043F12',
    borderRadius: 20,
    color: 'white',
    padding: 10,
    paddingHorizontal: 20,
    margin: 5,
  },
  unselectedText: {
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#043F12',
    padding: 10,
    paddingHorizontal: 20,
    margin: 5,
    color: '#043F12',
  },
  activityContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  genderContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  genderRow: {
    flexDirection: 'row',
    marginVertical: 10,
  },
  inputContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  counterContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  calorieCircle: {
    width: RFont(180),
    height: RFont(180),
    borderRadius: RFont(90),
    borderWidth: RFont(6),
    borderColor: '#FF3F1F',
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: RFont(32),
    shadowColor: 'black',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
    alignContent: 'center',
    alignSelf: 'center',
  },
  calorieValue: {
    ...fontStyles.Maison_600_20PX_28LH,
    color: '#FF3F1F',
    textAlign: 'center',
    fontWeight: 'bold',
  },
  calorieUnit: {
    ...fontStyles.Maison_400_14PX_16LH,
    color: 'gray',
    textAlign: 'center',
    marginTop: RFont(4),
  },
  errorText: {
    color: 'red',
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  headerContainer: {
    flexDirection: 'row',
    padding: 16,
    alignContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9F4ED',
  },
  logoStyle: {
    marginStart: 15,
  },
  mainView: {
    backgroundColor: '#F9F4ED',
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    alignContent: 'center',
    justifyContent: 'center',
  },
  loadingContent: {
    alignSelf: 'center',
    alignContent: 'center',
    alignItems: 'center',
    margin: 20,
    gap: 15,
  },
  loadingTitle: {
    textAlign: 'center',
    ...fontStyles.Maison_600_24PX_32LH,
  },
  loadingSubtitle: {
    textAlign: 'center',
    ...fontStyles.Maison_500_18PX_22LH,
    color: 'gray',
  },
  nextButtonContainer: {
    backgroundColor: 'white',
    padding: 16,
    flex: 1,
    borderTopEndRadius: 20,
    borderTopStartRadius: 20,
  },
  stepTitle: {
    ...fontStyles.Maison_600_32PX_40LH,
  },
});

const steps: StepsConfig = {
  loading: {
    title: 'Fueling your wellness journey',
    btnTxt: null,
    subTitle: 'Loading your goals and preferences…',
    type: 'staticText',
  },
  1: {
    title: "What's your current activity level?",
    btnTxt: 'Next',
    type: 'activitySelector',
    validationSchema: Yup.object().shape({
      activityLevel: Yup.string().required('Please select your activity level'),
    }),
  },
  2: {
    title: 'What is your birthdate?',
    btnTxt: 'Next',
    type: 'dateInput',
    validationSchema: Yup.object().shape({
      birthdate: Yup.date().required('Please select your birthdate'),
    }),
  },
  3: {
    title: 'Select your biological gender',
    btnTxt: 'Next',
    type: 'genderSelector',
    validationSchema: Yup.object().shape({
      gender: Yup.string().required('Please select your gender'),
    }),
  },
  4: {
    title: 'How tall are you?',
    btnTxt: 'Next',
    type: 'heightInput',
    validationSchema: Yup.object().shape({
      height: Yup.object().shape({
        value: Yup.number().required('Please enter your height'),
        unit: Yup.string().required(),
      }),
    }),
  },
  5: {
    title: "What's your current weight?",
    btnTxt: 'Next',
    type: 'weightInput',
    validationSchema: Yup.object().shape({
      weight: Yup.object().shape({
        value: Yup.number().required('Please enter your weight'),
        unit: Yup.string().required(),
      }),
    }),
  },
  6: {
    title: "What's your target weight?",
    btnTxt: 'Next',
    type: 'targetWeightInput',
    validationSchema: Yup.object().shape({
      targetWeight: Yup.object().shape({
        value: Yup.number().required('Please enter your target weight'),
        unit: Yup.string().required(),
      }),
    }),
  },
  7: {
    title: "What's your target steps?",
    btnTxt: 'Next',
    type: 'targetStepsInput',
    validationSchema: Yup.object().shape({
      targetSteps: Yup.number()
        .min(20, 'Target steps must be at least 20')
        .max(200, 'Target steps must be at most 200')
        .required('Please set your target steps'),
    }),
  },
  8: {
    title: 'Your target calorie?',
    btnTxt: 'Next',
    type: 'targetCaloriesInput',
    validationSchema: Yup.object().shape({
      targetCalories: Yup.number()
        .min(20, 'Target calories must be at least 20')
        .max(200, 'Target calories must be at most 200')
        .required('Please set your target calories'),
    }),
  },
  settingUpData: {
    title: 'Setting up your personal data',
    btnTxt: null,
    type: 'settingUpData',
  },
  10: {
    title: 'Introducing Your Personal AI Mentor',
    btnTxt: 'Next',
    subTitle: 'Find a mentor that resonates with your energy and aspirations.',
    type: 'staticText',
  },
  allDone: {
    title: 'All Done!',
    subTitle: 'Setting up your profile',
    btnTxt: null,
    type: 'allDone',
  },
};

const ActivitySelector: React.FC<ActivitySelectorProps> = ({
  value,
  onChange,
  error,
}) => {
  const activities: string[] = [
    'Little or no activity',
    'Some activity, 2–3x/week',
    'Regular, intense activity',
  ];

  return (
    <View style={style.activityContainer}>
      {activities.map((activity) => (
        <TouchableOpacity
          key={activity}
          onPress={() => onChange(activity)}
          activeOpacity={0.7}
        >
          <Text
            style={
              activity === value ? style.selectedText : style.unselectedText
            }
          >
            {activity}
          </Text>
        </TouchableOpacity>
      ))}
      {error && <Text style={style.errorText}>{error}</Text>}
    </View>
  );
};

const GenderSelector: React.FC<GenderSelectorProps> = ({
  value,
  onChange,
  error,
}) => {
  const genders: string[] = ['Male', 'Female'];

  return (
    <View style={style.genderContainer}>
      <View style={style.genderRow}>
        {genders.map((gender) => (
          <TouchableOpacity
            key={gender}
            onPress={() => onChange(gender)}
            activeOpacity={0.7}
          >
            <Text
              style={
                gender === value ? style.selectedText : style.unselectedText
              }
            >
              {gender}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
      {error && <Text style={style.errorText}>{error}</Text>}
    </View>
  );
};

const RenderStepComponent: React.FC<RenderStepComponentProps> = ({
  step,
  formik,
}) => {
  const today = new Date();
  const hundredYearsAgo = new Date(
    today.getFullYear() - 100,
    today.getMonth(),
    today.getDate(),
  );

  const heightInputRef = useRef<HeightOrWeightInputRef>(null);
  const weightInputRef = useRef<HeightOrWeightInputRef>(null);
  const targetWeightInputRef = useRef<HeightOrWeightInputRef>(null);

  switch (step?.type) {
    case 'staticText':
      return (
        <Text style={{ textAlign: 'center' }}>
          {step?.subTitle || step?.title}
        </Text>
      );

    case 'activitySelector':
      return (
        <ActivitySelector
          value={formik.values.activityLevel}
          onChange={(value) => formik.setFieldValue('activityLevel', value)}
          error={formik.touched.activityLevel && formik.errors.activityLevel}
        />
      );

    case 'dateInput':
      return (
        <View style={{ flex: 1, justifyContent: 'center' }}>
          <StepperDateInput
            value={formik.values.birthdate}
            onChangeText={(date) => formik.setFieldValue('birthdate', date)}
            minimumDate={hundredYearsAgo}
            maximumDate={new Date()}
          />
          {formik.touched.birthdate && formik.errors.birthdate && (
            <Text style={style.errorText}>{formik.errors.birthdate}</Text>
          )}
        </View>
      );

    case 'genderSelector':
      return (
        <GenderSelector
          value={formik.values.gender}
          onChange={(value) => formik.setFieldValue('gender', value)}
          error={formik.touched.gender && formik.errors.gender}
        />
      );

    case 'heightInput':
      return (
        <View style={style.inputContainer}>
          <HeightOrWeightInput
            ref={heightInputRef}
            inputType="height"
            onSubmit={(data) => formik.setFieldValue('height', data)}
          />
          {formik.touched.height && formik.errors.height && (
            <Text style={style.errorText}>
              {typeof formik.errors.height === 'string'
                ? formik.errors.height
                : formik.errors.height?.value}
            </Text>
          )}
        </View>
      );

    case 'weightInput':
      return (
        <View style={style.inputContainer}>
          <HeightOrWeightInput
            ref={weightInputRef}
            inputType="weight"
            onSubmit={(data) => formik.setFieldValue('weight', data)}
          />
          {formik.touched.weight && formik.errors.weight && (
            <Text style={style.errorText}>
              {typeof formik.errors.weight === 'string'
                ? formik.errors.weight
                : formik.errors.weight?.value}
            </Text>
          )}
        </View>
      );

    case 'targetWeightInput':
      return (
        <View style={style.inputContainer}>
          <HeightOrWeightInput
            ref={targetWeightInputRef}
            inputType="weight"
            onSubmit={(data) => formik.setFieldValue('targetWeight', data)}
          />
          {formik.touched.targetWeight && formik.errors.targetWeight && (
            <Text style={style.errorText}>
              {typeof formik.errors.targetWeight === 'string'
                ? formik.errors.targetWeight
                : formik.errors.targetWeight?.value}
            </Text>
          )}
        </View>
      );

    case 'targetStepsInput':
      return (
        <View style={style.counterContainer}>
          <AdjustmentCounter
            value={formik.values.targetSteps}
            onIncrement={() =>
              formik.setFieldValue('targetSteps', formik.values.targetSteps + 1)
            }
            onDecrement={() =>
              formik.setFieldValue('targetSteps', formik.values.targetSteps - 1)
            }
            disabledIncrement={formik.values.targetSteps >= 200}
            disabledDecrement={formik.values.targetSteps <= 20}
          />
          {formik.touched.targetSteps && formik.errors.targetSteps && (
            <Text style={style.errorText}>{formik.errors.targetSteps}</Text>
          )}
        </View>
      );

    case 'targetCaloriesInput':
      return (
        <View style={style.counterContainer}>
          <View style={style.calorieCircle}>
            <View style={{ marginBottom: RFont(8) }}>
              <KcaConsumedIcon2
                height={RFont(32)}
                width={RFont(32)}
                color={'#FF3F1F'}
                stroke={'#FF3F1F'}
              />
            </View>
            <Text style={style.calorieValue}>
              {formik.values.targetCalories}
            </Text>
            <Text style={style.calorieUnit}>kcal</Text>
          </View>
          <AdjustmentCounter
            value={formik.values.targetCalories}
            onIncrement={() =>
              formik.setFieldValue(
                'targetCalories',
                formik.values.targetCalories + 1,
              )
            }
            onDecrement={() =>
              formik.setFieldValue(
                'targetCalories',
                formik.values.targetCalories - 1,
              )
            }
            disabledIncrement={formik.values.targetCalories >= 200}
            disabledDecrement={formik.values.targetCalories <= 20}
          />
          {formik.touched.targetCalories && formik.errors.targetCalories && (
            <Text style={style.errorText}>{formik.errors.targetCalories}</Text>
          )}
        </View>
      );

    default:
      return <Text>Unknown step</Text>;
  }
};

const OnBoarding: React.FC<GuestScreenProps<'OnBoarding'>> = ({
  navigation,
  route,
}: {
  navigation: NavigationProp<RootGuestStackParamList>;
  route: RouteProp<RootGuestStackParamList, 'OnBoarding'>;
}) => {
  const [currentStep, setStep] = useState<string | number>('loading');
  const [formValues, setFormValues] = useState<OnBoardingFormValues>({
    activityLevel: '',
    birthdate: '',
    gender: '',
    height: { value: 0, unit: 'cm' },
    weight: { value: 0, unit: 'kg' },
    targetWeight: { value: 0, unit: 'kg' },
    targetSteps: 100,
    targetCalories: 100,
  });

  // Get validation schema for current step
  const currentValidationSchema =
    steps[currentStep]?.validationSchema || Yup.object();

  // Define step progression map
  const stepProgressionMap: Record<string | number, string | number> = {
    loading: 1,
    1: 2,
    2: 3,
    3: 4,
    4: 5,
    5: 6,
    6: 7,
    7: 8,
    8: 'settingUpData',
    settingUpData: 10,
    10: 'allDone',
  };

  const formik = useFormik<OnBoardingFormValues>({
    initialValues: formValues,
    validationSchema: currentValidationSchema,
    enableReinitialize: true,
    validateOnChange: false,
    validateOnBlur: true,
    onSubmit: (values) => {
      // Update form values
      setFormValues((prev) => ({ ...prev, ...values }));

      // Move to next step
      const nextStep = stepProgressionMap[currentStep];

      if (nextStep) {
        setStep(nextStep);
      }
    },
  });

  // Handle initial loading and transitions
  useEffect(() => {
    if (['loading', 'settingUpData'].includes(currentStep as string)) {
      const timer = setTimeout(() => {
        if (currentStep === 'loading') {
          setStep(1);
        } else if (currentStep === 'settingUpData') {
          setStep(10);
        }
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [currentStep]);

  // Handle form submission when Next button is clicked
  const handleNextPress = () => {
    formik.handleSubmit();
  };

  // Check if current step is a loading/static step
  const isLoadingStep = ['loading', 'settingUpData', 'allDone'].includes(
    currentStep as string,
  );

  // Get current step configuration
  const currentStepConfig = steps[currentStep];

  return (
    <React.Fragment>
      <View style={style.headerContainer}>
        <LeftArrow />
        <Logo style={style.logoStyle} />
      </View>

      <View style={style.mainView}>
        {isLoadingStep ? (
          <View style={style.loadingContainer}>
            <View style={style.loadingContent}>
              <Loader />
              <Text style={style.loadingTitle}>{currentStepConfig?.title}</Text>
              {currentStepConfig?.subTitle && (
                <Text style={style.loadingSubtitle}>
                  {currentStepConfig.subTitle}
                </Text>
              )}
            </View>
          </View>
        ) : (
          <View style={style.mainContainer}>
            <View>
              <Text style={style.stepTitle}>{currentStepConfig?.title}</Text>
              {currentStepConfig?.subTitle && (
                <Text>{currentStepConfig.subTitle}</Text>
              )}
            </View>
            <RenderStepComponent
              step={currentStepConfig as StepConfig}
              formik={formik}
            />
          </View>
        )}

        {currentStepConfig?.btnTxt && (
          <View style={style.nextButtonContainer}>
            <PrimaryBtn
              text={currentStepConfig.btnTxt}
              onPress={handleNextPress}
            />
          </View>
        )}
      </View>
    </React.Fragment>
  );
};

export default OnBoarding;
