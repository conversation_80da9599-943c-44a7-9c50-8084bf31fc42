import messaging from '@react-native-firebase/messaging';
import {
  GoogleSignin,
  statusCodes,
} from '@react-native-google-signin/google-signin';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { useState } from 'react';
import { Image, Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import { RootGuestStackParamList } from '../../../../@types/navigation';
import { GoogleLogo } from '../../../assets/images';
import { RootState } from '../../../redux/store';
import {
  ensureUserModuleSyncEntries,
  supabase,
} from '../../../screens/fitness/supabaseClient';
import { useTheme } from '../../../theme';
import { fontStyles } from '../../../theme/fonts';
import {
  marginStyles,
  paddingStyles,
} from '../../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../../theme/styles/globalStyles';
import { storeDataToAsyncStorage } from '../../../utils/functions';
import { PrimaryBtn } from '../../Buttons/Btns';

export const WelcomeCard = () =>
  // navigation: NavigationProp<RootGuestStackParamList>,
  {
    const { translation } = useSelector((state: RootState) => state.auth);
    const { isRTL } = useSelector((state: RootState) => state.app);
    const colors = useTheme();
    const navigation = useNavigation<NavigationProp<RootGuestStackParamList>>();
    const [loading, setLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
   const signIn = async () => {
      try {
        setLoading(true);
        setError(null);
        await GoogleSignin.hasPlayServices();
        const usrInfo = await GoogleSignin.signIn();
        const fcmToken = await messaging().getToken();
        console.log('FCM Token:', fcmToken);
        console.log('info', `FCM Token: ${fcmToken}`, 'Login');

        // 1. Sign in to Supabase using Google idToken
        const { error: authError } = await supabase.auth.signInWithIdToken({
          provider: 'google',
          token: usrInfo.data?.idToken as string,
        });
        if (authError) {
          console.error('Supabase Auth Error:', authError);
          console.log(
            'error',
            `Supabase Auth Error: ${authError.message}`,
            'Login',
          );
          return;
        }
        // 2. Get authenticated user
        const {
          data: { user },
        } = await supabase.auth.getUser();
        // 3. Check if user exists in your users table
        const { data: existingUser, error: existingUserError } = await supabase
          .from('users')
          .select('*')
          .eq('id', user?.id)
          .single();
        if (existingUserError && existingUserError.code !== 'PGRST116') {
          // PGRST116 = no rows found, not an error
          console.error('Error checking existing user:', existingUserError);
          console.log(
            'error',
            `Error checking existing user: ${existingUserError.message}`,
            'Login',
          );
          return;
        }
        if (!existingUser) {
          // Insert user with last_synced_at = null initially
          const { error: insertError } = await supabase.from('users').insert({
            id: user?.id,
            email: user?.email,
            full_name:
              user?.user_metadata?.full_name || usrInfo?.data?.user?.name,
            created_at: new Date().toISOString(),
            fcm_token: fcmToken, // ✅ Include FCM token here
            is_verified: false,
          });
          if (insertError) {
            console.error('Error inserting profile:', insertError);
            console.log(
              'error',
              `Error inserting profile: ${insertError.message}`,
              'Login',
            );

            return;
          } else {
            console.log('New user and fitness profile created');
            console.log(
              'info',
              'New user and fitness profile created',
              'Login',
            );

            console.log(':white_tick: New profile created', existingUser);
          }
        } else {
          console.log(':repeat: User already exists');
          console.log('info', 'User already exists', 'Login');
          // Update FCM token for existing user
          const { data: updatedData, error: updateError } = await supabase
            .from('users')
            .update({ fcm_token: fcmToken })
            .eq('id', user?.id)
            .select();
          if (updateError) {
            console.error('Error updating FCM token:', updateError);
            console.log(
              'error',
              `Error updating FCM token: ${updateError.message}`,
              'Login',
            );
          } else {
            console.log('FCM token updated for existing user:', updatedData);
            console.log(
              'info',
              `FCM token updated for existing user: ${JSON.stringify(updatedData)}`,
              'Login',
            );
          }
        }

        const { data: existingUserFitnessData } = await supabase
          .from('user_fitness_profiles')
          .select('id')
          .eq('user_id', user?.id)
          .single();

        // const { data: existing } = await supabase
        //   .from('user_module_sync')
        //   .select('id')
        //   .eq('user_id', user?.id)
        //   .eq('module_name', 'steps')
        //   .single();
        // if (!existing) {
        //   console.log(
        //     'info',
        //     'Creating initial user module sync entry...',
        //     'Login',
        //   );
        //   const { error: insertError } = await supabase
        //     .from('user_module_sync')
        //     .insert({
        //       user_id: user?.id,
        //       module_name: 'steps',
        //       last_synced_at: null,
        //       created_at: new Date().toISOString(),
        //       updated_at: new Date().toISOString(),
        //     });
        //   if (insertError) {
        //     console.error(
        //       'Error inserting initial user_module_sync entry:',
        //       insertError,
        //     );
        //   }
        // }
        await ensureUserModuleSyncEntries(user?.id as string);
        console.log({ existingUserFitnessData });
        storeDataToAsyncStorage('userId', user?.id ?? '');
        if (!existingUserFitnessData) {
          storeDataToAsyncStorage('isNewUser', 'true');
        }
        navigation.navigate('Tabs', { tokenRefresher: 'tokenRefresh' });
      } catch (err: any) {
        setError(err);
        if (err.code === statusCodes.SIGN_IN_CANCELLED) {
          console.log('User cancelled the login flow');
        } else if (err.code === statusCodes.IN_PROGRESS) {
          console.log('Operation is in progress already');
        } else if (err.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
          console.log(
            'Google Play Services Error',
            'Please update or enable Google Play Services to use Google Sign-In.',
          );
        } else {
          console.log(
            'Sign In Error',
            'An unexpected error occurred. Please try again.',
          );
        }
      }
    };

    return (
      <View
        style={[
          globalStyles.flex1,
          globalStyles.justifyContentEnd,
          paddingStyles.py12,
          paddingStyles.px16,
        ]}
      >
        <Text
          style={[
            fontStyles.Maison_600_48PX_53LH,
            isRTL ? globalStyles.textRight : globalStyles.textLeft,
            { color: colors?.neutral_white },
          ]}
        >
          {translation.YOUR_WELLNESS_JOURNEY_STARTS_HERE}
        </Text>
        <PrimaryBtn
          text={translation.LOGIN_WITH_GOOGLE}
          onPress={signIn}
          isLoading={loading}
          leftIcon={
            <Image
              source={GoogleLogo}
              height={10}
              width={10}
              style={{ width: 20, height: 20 }}
            />
          }
          style={[marginStyles.mt_32, marginStyles.mb_24]}
          textStyle={[
            { color: colors?.black },
            fontStyles.Maison_600_16PX_19_2LH,
          ]}
          // focusColor={colors?.primary_grenade}
          backgroundColor={colors?.neutral_white}
        />
      </View>
    );
  };
