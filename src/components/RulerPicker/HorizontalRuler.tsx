/* eslint-disable react-native/no-inline-styles */
import React, { useEffect, useRef, useState } from 'react';
import {
  Dimensions,
  NativeScrollEvent,
  NativeSyntheticEvent,
  ScrollView,
  Text,
  TextInput,
  View,
} from 'react-native';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import {
  gapStyles,
  marginStyles,
} from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';
import { createRulerScreenStyles } from '../../theme/styles/rulerPickerStyles';
import { PrimaryBtn } from '../Buttons/Btns';
import Divider from '../Divider';

const ITEM_WIDTH = 14;
const SCREEN_WIDTH = Dimensions.get('window').width;

interface HorizontalRulerProps {
  unit: 'kg' | 'lbs';
  min: number;
  max: number;
  activeUnit?: string;
  unitData: Array<{
    label: string;
    value: string;
  }>;
  // eslint-disable-next-line no-unused-vars
  onChange?: (value: number) => void;
  title?: string;
  // eslint-disable-next-line no-unused-vars
  onPress: (value: string) => void;
}

export const HorizontalRuler = ({
  unit,
  unitData,
  activeUnit,
  onPress,
  min,
  max,
  onChange,
  title = 'Select Weight',
}: HorizontalRulerProps) => {
  const scrollRef = useRef<ScrollView>(null);
  const [value, setValue] = useState<number>(min);

  const data = Array.from({ length: max - min + 1 }, (_, i) => min + i);
  const centerOffset = SCREEN_WIDTH / 2 - ITEM_WIDTH;

  const handleScroll = (e: NativeSyntheticEvent<NativeScrollEvent>) => {
    const offsetX = e.nativeEvent.contentOffset.x;
    const index = Math.round(offsetX / ITEM_WIDTH);
    const newValue = data[index];

    if (newValue !== undefined) {
      setValue(newValue);
      onChange?.(newValue);
    }
  };

  const scrollToInitial = () => {
    const index = data.indexOf(value);
    if (scrollRef.current && index >= 0) {
      scrollRef.current.scrollTo({
        x: index * ITEM_WIDTH,
        animated: false,
      });
    }
  };

  const scrollToValue = (val: number) => {
    const index = data.indexOf(val);
    if (scrollRef.current && index >= 0) {
      scrollRef.current.scrollTo({
        x: index * ITEM_WIDTH,
        animated: true,
      });
    }
  };

  useEffect(() => {
    scrollToInitial();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const colors = useTheme();
  const rulerStyles = createRulerScreenStyles(colors);

  return (
    <View style={[rulerStyles.container, { justifyContent: 'space-between' }]}>
      <Text
        style={[fontStyles.Maison_600_32PX_40LH, { color: colors?.grey_900 }]}
      >
        {title}
      </Text>
      <View style={[globalStyles.alignItemsCenter, globalStyles.alignSelfEnd]}>
        <TextInput
          value={String(value)}
          style={[
            fontStyles.Maison_600_24PX_32LH,
            { color: colors?.neutral_black, textAlign: 'center', width: 80 },
          ]}
          keyboardType="numeric"
          onChangeText={(e) => {
            const val = parseFloat(e);
            if (!isNaN(val)) {
              setValue(val);
              scrollToValue(val);
            } else {
              setValue(0);
            }
          }}
        />
        <Divider color={colors?.grey_100} thickness={1} />
        <View style={[globalStyles.row, gapStyles.gap_8, marginStyles.mt_20]}>
          {unitData?.map((item) => (
            <PrimaryBtn
              key={item?.value}
              text={item?.label}
              textStyle={[
                fontStyles.Maison_500_18PX_21_6LH,
                {
                  color:
                    item?.value === activeUnit
                      ? colors?.primary_cream
                      : colors?.grey_900,
                },
              ]}
              style={[
                rulerStyles.rulerPrimaryBtn,
                {
                  backgroundColor:
                    item?.value === activeUnit
                      ? colors?.primary_spinach
                      : colors?.neutral_white,
                },
              ]}
              onPress={() => onPress(item?.value)}
            />
          ))}
        </View>
      </View>
      <View style={[globalStyles.row, globalStyles.justifyContentSpaceBetween]}>
        <View>
          <View
            style={{
              position: 'absolute',
              top: 0,
              bottom: 0,
              left: SCREEN_WIDTH / 2 - 1,
              width: 2,
              backgroundColor: colors.primary_spinach,
              zIndex: 1,
            }}
          />
          <ScrollView
            ref={scrollRef}
            horizontal
            onScroll={handleScroll}
            scrollEventThrottle={10}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{
              paddingHorizontal: centerOffset,
              paddingBottom: 16,
            }}
            style={{ marginTop: 20 }}
          >
            {data.map((val, index) => (
              <View
                key={val}
                style={{
                  alignItems: 'center',
                  width: ITEM_WIDTH,
                }}
              >
                <View
                  style={{
                    width: 2,
                    height: index % 10 === 0 ? 24 : index % 5 === 0 ? 16 : 8,
                    backgroundColor:
                      index % 5 === 0 ? colors.grey_600 : colors.grey_300,
                  }}
                />
                {index % 10 === 0 && (
                  <Text
                    style={[
                      fontStyles.Maison_500_12PX_15_6LH,
                      { color: colors.grey_500, marginTop: 4 },
                    ]}
                  >
                    {val}
                  </Text>
                )}
              </View>
            ))}
          </ScrollView>
        </View>
      </View>
    </View>
  );
};
