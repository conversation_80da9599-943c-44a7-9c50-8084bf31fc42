/* eslint-disable react-native/no-inline-styles */
import React, { FC } from 'react';
import { View } from 'react-native';
import { useTheme } from '../../theme';
import { globalStyles } from '../../theme/styles/globalStyles';
import { createRulerScreenStyles } from '../../theme/styles/rulerPickerStyles';

interface RulerPickerIndicatorProps {
  centerOffset: number;
  value: number;
  activeUnit?: string;
  // eslint-disable-next-line no-unused-vars
  onPress: (value: string) => void;
  unitData?: Array<{ label: string; value: string }>;
  unit?: 'cm' | 'kg' | 'lbs' | 'ft-in';
}

const RulerPickerIndicator: FC<RulerPickerIndicatorProps> = ({
  centerOffset,
}) => {
  const color = useTheme();
  const RulerScreenStyles = createRulerScreenStyles(color);
  return (
    <View
      style={[
        globalStyles.rowReverse,
        globalStyles.absolute,
        globalStyles.alignItemsCenter,
        {
          display: 'flex',
          top: centerOffset,
          zIndex: 1,
        },
      ]}
    >
      <View
        style={[
          globalStyles.row,
          globalStyles.justifyContentEnd,
          globalStyles.alignItemsCenter,
        ]}
      >
        <View style={RulerScreenStyles?.triangle} />
        <View style={RulerScreenStyles?.indicatorLine} />
      </View>
    </View>
  );
};

export default RulerPickerIndicator;
