# Coach Chat UI Components

This directory contains the new AI coach chat UI components for the fitness app.

## Components

### 1. CoachChatUI.tsx

The main chat UI component that provides a modern, mobile-optimized chat interface.

**Features:**

- ✅ Top navigation with back arrow, coach name, and avatar
- ✅ Date separators for message grouping
- ✅ Alternating message bubbles (coach: white, user: beige)
- ✅ Timestamps with delivery status (✓✓)
- ✅ Multiple-choice option buttons
- ✅ Metric display cards for fitness data
- ✅ Emoji reaction buttons (👍 👎)
- ✅ Text input with + button and send button
- ✅ Keyboard handling with KeyboardAvoidingView
- ✅ Light beige background (#FDF5EF)
- ✅ Scrollable chat content
- ✅ Loading states

**Props:**

```typescript
interface CoachChatUIProps {
  coachName: string;
  coachAvatar?: string;
  messages: Message[];
  onSendMessage: (message: string) => void;
  onOptionSelect: (option: string) => void;
  onBackPress: () => void;
  inputValue: string;
  setInputValue: (value: string) => void;
  isLoading?: boolean;
}
```

### 2. CoachChatScreen.tsx

A screen component that integrates with your existing Supabase backend and API.

**Features:**

- ✅ User authentication integration
- ✅ Coach data fetching
- ✅ Chat history loading
- ✅ API integration for sending messages
- ✅ Error handling

### 3. CoachChatDemo.tsx

A demo screen with sample data to showcase all chat features.

**Features:**

- ✅ Sample conversation with various message types
- ✅ Simulated API responses
- ✅ All UI features demonstrated

## Message Types

### Text Message

```typescript
{
  id: string;
  sender: 'user' | 'coach';
  text: string;
  timestamp: string;
  type: 'text';
  options?: string[]; // Optional action buttons
}
```

### Metric Message

```typescript
{
  id: string;
  sender: 'coach';
  text: string;
  timestamp: string;
  type: 'metric';
  metrics: {
    water: {
      current: number;
      target: number;
    }
    meals: number;
    steps: {
      current: number;
      target: number;
    }
    sleep: string;
  }
}
```

## Usage

### Basic Implementation

```typescript
import CoachChatUI from './CoachChatUI';

const MyChatScreen = () => {
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState('');

  const handleSendMessage = (message: string) => {
    // Your message sending logic
  };

  const handleOptionSelect = (option: string) => {
    // Handle option button press
  };

  return (
    <CoachChatUI
      coachName="Raj"
      messages={messages}
      onSendMessage={handleSendMessage}
      onOptionSelect={handleOptionSelect}
      onBackPress={() => navigation.goBack()}
      inputValue={inputValue}
      setInputValue={setInputValue}
    />
  );
};
```

### Navigation

Add to your navigation stack:

```typescript
<Stack.Screen
  name="CoachChatDemo"
  component={CoachChatDemo}
/>
```

Navigate to the chat:

```typescript
navigation.navigate('CoachChatDemo');
```

## Styling

The component uses a light beige theme (#FDF5EF) with:

- White coach message bubbles
- Beige user message bubbles (#E8D5B7)
- Red send button (#FF3F1F)
- Subtle shadows and rounded corners
- Mobile-optimized spacing and typography

## Testing

Use the `ChatTestButton` component to quickly navigate to the demo:

```typescript
import ChatTestButton from './ChatTestButton';

// Add to any screen
<ChatTestButton />
```

## Integration with Existing Code

The new chat UI can replace the existing `chatui.tsx` component. Update your imports:

```typescript
// Old
import ChatUI from './chatui';

// New
import CoachChatUI from './CoachChatUI';
```

The new component provides a much more sophisticated and mobile-friendly chat experience while maintaining compatibility with your existing API structure.
