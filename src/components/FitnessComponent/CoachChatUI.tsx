import React, { useEffect, useRef } from 'react';
import {
  Dimensions,
  Keyboard,
  KeyboardAvoidingView,
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import { LeftArrow } from '../../assets/images';
import MessageDotLoader from '../common/MessageDotLoader';

const { width } = Dimensions.get('window');

interface Message {
  id: string;
  sender: 'user' | 'coach';
  text: string;
  timestamp: string;
  type?: 'text' | 'metric' | 'action';
  options?: string[];
  metrics?: {
    water: { current: number; target: number };
    meals: number;
    steps: { current: number; target: number };
    sleep: string;
  };
  delivered?: boolean;
}

interface CoachChatUIProps {
  coachName: string;
  coachAvatar?: string;
  messages: Message[];
  onSendMessage: (message: string) => void;
  onOptionSelect: (option: string) => void;
  onBackPress: () => void;
  inputValue: string;
  setInputValue: (value: string) => void;
  isLoading?: boolean;
}

const CoachChatUI: React.FC<CoachChatUIProps> = ({
  coachName,
  coachAvatar,
  messages,
  onSendMessage,
  onOptionSelect,
  onBackPress,
  inputValue,
  setInputValue,
  isLoading = false,
}) => {
  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    if (messages.length > 0) {
      // Add delay to ensure UI has rendered before scrolling
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 200);
    }
  }, [messages]);

  // Function to scroll to end with proper timing
  const scrollToEndWithDelay = () => {
    // Use multiple timeouts to ensure scrolling works after keyboard animation
    // setTimeout(() => {
    //   scrollViewRef.current?.scrollToEnd({ animated: true });
    // }, 100);

    // setTimeout(() => {
    //   scrollViewRef.current?.scrollToEnd({ animated: true });
    // }, 300);

    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, 500);
  };

  // Add keyboard event listeners for better scroll handling
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        // Scroll to end when keyboard appears
        setTimeout(() => {
          scrollViewRef.current?.scrollToEnd({ animated: true });
        }, 100);
      },
    );

    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        // Optional: Handle keyboard hide if needed
      },
    );

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, []);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        day: 'numeric',
        month: 'short',
        year: '2-digit',
      });
    }
  };

  const renderMessage = (message: Message, index: number) => {
    const isCoach = message.sender === 'coach';
    const showDateSeparator =
      index === 0 ||
      new Date(messages[index - 1].timestamp).toDateString() !==
        new Date(message.timestamp).toDateString();

    // Check if this is the latest coach message
    const isLatestCoachMessage = isCoach && index === messages.length - 1;

    return (
      <View key={message.id}>
        {showDateSeparator && (
          <View style={styles.dateSeparator}>
            <Text style={styles.dateText}>{formatDate(message.timestamp)}</Text>
          </View>
        )}

        <View
          style={[
            styles.messageContainer,
            isCoach
              ? styles.coachMessageContainer
              : styles.userMessageContainer,
          ]}
        >
          {isCoach ? (
            <View style={styles.coachMessage}>
              <View style={styles.coachBubble}>
                {message.type === 'metric' && message.metrics ? (
                  <View style={styles.metricCard}>
                    <View style={styles.metricRow}>
                      <Text style={styles.metricItem}>
                        🥤 Water: {message.metrics.water.current}/
                        {message.metrics.water.target} glasses
                      </Text>
                    </View>
                    <View style={styles.metricRow}>
                      <Text style={styles.metricItem}>
                        🥗 Meals: {message.metrics.meals}
                      </Text>
                    </View>
                    <View style={styles.metricRow}>
                      <Text style={styles.metricItem}>
                        🚶‍♂️ Steps: {message.metrics.steps.current}/
                        {message.metrics.steps.target}
                      </Text>
                    </View>
                    <View style={styles.metricRow}>
                      <Text style={styles.metricItem}>
                        😴 Sleep: {message.metrics.sleep}
                      </Text>
                    </View>
                  </View>
                ) : (
                  <>
                    <Text style={styles.coachMessageText}>{message.text}</Text>
                    <Text style={styles.timestampInBubbleCoach}>
                      {new Date(message.timestamp).toLocaleTimeString('en-US', {
                        hour: '2-digit',
                        minute: '2-digit',
                      })}
                    </Text>
                  </>
                )}
              </View>
              {/* <Text style={styles.timestamp}>
                {new Date(message.timestamp).toLocaleTimeString('en-US', {
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </Text> */}

              {/* Only show options on the latest coach message */}
              {isLatestCoachMessage &&
                message.options &&
                message.options.length > 0 && (
                  <View style={styles.optionsContainer}>
                    {message.options.map((option, optionIndex) => (
                      <TouchableOpacity
                        key={optionIndex}
                        style={styles.optionButton}
                        onPress={() => onOptionSelect(option)}
                      >
                        <Text style={styles.optionText}>{option}</Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                )}

              {/* Only show emoji reactions on the latest coach message */}
              {/* {isLatestCoachMessage && (
                <View style={styles.emojiContainer}>
                  <TouchableOpacity
                    style={styles.emojiButton}
                    onPress={() => onOptionSelect('👍')}
                  >
                    <Text style={styles.emojiText}>👍</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.emojiButton}
                    onPress={() => onOptionSelect('👎')}
                  >
                    <Text style={styles.emojiText}>👎</Text>
                  </TouchableOpacity>
                </View>
              )} */}
            </View>
          ) : (
            <View style={styles.userMessage}>
              <View style={styles.userBubble}>
                <Text style={styles.userMessageText}>{message.text}</Text>
                <View style={styles.bubbleFooter}>
                  <Text style={styles.timestampInBubble}>
                    {new Date(message.timestamp).toLocaleTimeString('en-US', {
                      hour: '2-digit',
                      minute: '2-digit',
                    })}
                  </Text>
                  <Text style={styles.deliveryStatusInBubble}>
                    {message.delivered ? '✓✓' : '✓'}
                  </Text>
                </View>
              </View>
            </View>
          )}
        </View>
      </View>
    );
  };

  const handleSend = () => {
    if (inputValue.trim()) {
      onSendMessage(inputValue.trim());
      setInputValue('');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FDF5EF" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onBackPress} style={styles.backButton}>
          <LeftArrow />
        </TouchableOpacity>

        <View style={styles.coachInfo}>
          <View style={styles.coachAvatar}>
            <Text style={styles.avatarText}>{coachName.charAt(0)}</Text>
          </View>
          <Text style={styles.coachNameHeader}>{coachName}</Text>
        </View>

        <View style={styles.headerSpacer} />
      </View>

      <KeyboardAvoidingView
        style={styles.chatContainer}
        // behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        // keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 40}
      >
        {/* Chat Messages */}
        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          contentContainerStyle={styles.messagesContent}
          showsVerticalScrollIndicator={false}
        >
          {messages.map((message, index) => renderMessage(message, index))}
          {isLoading && (
            <View style={styles.loadingMessage}>
              <View style={styles.coachBubble}>
                <MessageDotLoader color="#FF6B35" size={8} />
              </View>
            </View>
          )}
        </ScrollView>

        {/* Input Area */}
        <View style={styles.inputContainer}>
          {/* <TouchableOpacity style={styles.addButton}>
            <Text style={styles.addButtonText}>+</Text>
          </TouchableOpacity> */}

          <TextInput
            style={styles.textInput}
            placeholder="Type in your message…"
            placeholderTextColor="#999"
            value={inputValue}
            onChangeText={setInputValue}
            multiline
            maxLength={500}
            onFocus={() => {
              console.log('Input focused - scrolling to end');
              scrollToEndWithDelay();
            }}
          />

          <TouchableOpacity onPress={handleSend} disabled={!inputValue.trim()}>
            <View style={styles.sendButton}>
              <Icon name="send" color={'#fff'} size={20} />
            </View>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FDF5EF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FDF5EF',
    borderBottomWidth: 1,
    borderBottomColor: '#F0E6D6',
  },
  backButton: {
    color: '#333',
    fontSize: 24,
    fontWeight: 'bold',
    padding: 8,
  },
  backArrow: {
    fontSize: 24,
    color: '#333',
    fontWeight: 'bold',
  },
  bubbleFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginTop: 4,
    gap: 4,
  },

  timestampInBubble: {
    fontSize: 10,
    // color: '#ccc',
    marginTop: 4,
    alignSelf: 'flex-end',
  },
  timestampInBubbleCoach: {
    fontSize: 10,
    // color: '#ccc',
    marginTop: 10,
    alignSelf: 'flex-start',
  },
  deliveryStatusInBubble: {
    fontSize: 12,
    color: '#4CAF50',
  },

  coachInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginLeft: 12, // Compensate for back button
  },
  coachAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FF6B35',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  avatarText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  coachNameHeader: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  headerSpacer: {
    width: 32,
  },
  chatContainer: {
    flex: 1,
  },
  messagesContainer: {
    flex: 1,
  },
  messagesContent: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  dateSeparator: {
    alignItems: 'center',
    marginVertical: 16,
  },
  dateText: {
    fontSize: 14,
    color: '#666',
    backgroundColor: '#F0E6D6',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  messageContainer: {
    marginVertical: 4,
  },
  coachMessageContainer: {
    alignItems: 'flex-start',
  },
  userMessageContainer: {
    alignItems: 'flex-end',
  },
  coachMessage: {
    maxWidth: width * 0.8,
  },
  userMessage: {
    maxWidth: width * 0.8,
    alignItems: 'flex-end',
  },
  coachBubble: {
    backgroundColor: 'white',
    borderRadius: 18,
    borderBottomLeftRadius: 4,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  userBubble: {
    backgroundColor: '#ECE1D7',
    borderRadius: 18,
    borderBottomRightRadius: 4,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  coachMessageText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 22,
  },
  userMessageText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 22,
  },
  metricCard: {
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 12,
    borderWidth: 1,
    borderColor: '#E9ECEF',
  },
  metricRow: {
    marginVertical: 2,
  },
  metricItem: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
  },
  timestamp: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  userTimestampContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  deliveryStatus: {
    fontSize: 12,
    color: '#4CAF50',
    marginLeft: 4,
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
    gap: 8,
  },
  optionButton: {
    backgroundColor: '#fff',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    // borderWidth: 1,
    // borderColor: '#DDD',
  },
  optionText: {
    fontSize: 14,
    color: '#FF6B35',
    fontWeight: 600,
  },
  emojiContainer: {
    flexDirection: 'row',
    marginTop: 8,
    gap: 8,
  },
  emojiButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  emojiText: {
    fontSize: 18,
  },
  loadingMessage: {
    alignItems: 'flex-start',
    marginVertical: 4,
  },
  inputContainer: {
    alignItems: 'flex-end',
    backgroundColor: '#FFFFFF',
    borderTopColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    borderTopWidth: 1,
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F0F0F0',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  addButtonText: {
    fontSize: 20,
    color: '#666',
    fontWeight: 'bold',
  },
  textInput: {
    backgroundColor: 'white',
    borderColor: '#E5E5E6',
    borderRadius: 20,
    borderWidth: 1,
    color: '#62656A',
    flex: 1,
    fontSize: 16,
    fontWeight: 500,
    maxHeight: 100,
    paddingHorizontal: 16,
    paddingVertical: 10,
  },
  sendButton: {
    alignItems: 'center',
    backgroundColor: '#FF3F1F',
    borderRadius: 20,
    height: 40,
    justifyContent: 'center',
    marginLeft: 8,
    padding: 5,
    paddingRight: 7,
    paddingTop: 7,
    width: 40,
  },
  sendButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default CoachChatUI;
