import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  Dimensions,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  TextStyle,
  View,
  ViewStyle,
} from 'react-native';
import {
  KcaConsumedIcon3,
  MinusRoundedIcon,
  PlusOrangeIcon,
  Walk,
  Weight,
} from '../../assets/images';
import { useTheme } from '../../theme';
import { fontStyles, RFont } from '../../theme/fonts';
import { PrimaryBtn } from '../Buttons/Btns';
import { GorhomBottomSheet } from './BottomSheet';

// Type definitions
interface GoalBottomSheetProps {
  isOpen: boolean;
  onClose: () => void;
  goalType: 'food' | 'steps' | 'weight';
  onSave: (value: number) => void;
  currentValue?: number; // Optional current value from localStorage
}

type WeightUnit = 'kg' | 'lbs';

interface GoalConfig {
  title: string;
  icon: React.ComponentType<any>;
  unit: string;
  defaultValue: number;
  step: number;
  min: number;
  max: number;
  displayFormat: (value: number) => string;
}

// Goal configurations
const GOAL_CONFIGS: Record<'food' | 'steps' | 'weight', GoalConfig> = {
  food: {
    title: 'Calories',
    icon: KcaConsumedIcon3,
    unit: 'kcal/day',
    defaultValue: 2000,
    step: 50,
    min: 1000,
    max: 4000,
    displayFormat: (value: number) => `${value} kcal/day`,
  },
  steps: {
    title: 'Steps',
    icon: Walk,
    unit: 'steps/day',
    defaultValue: 6000,
    step: 500,
    min: 1000,
    max: 20000,
    displayFormat: (value: number) => `${value.toLocaleString()} steps/day`,
  },
  weight: {
    title: 'Weight',
    icon: Weight,
    unit: 'kg',
    defaultValue: 65,
    step: 0.5,
    min: 30,
    max: 200,
    displayFormat: (value: number) => `${value}kg`,
  },
};

const GoalBottomSheet: React.FC<GoalBottomSheetProps> = ({
  isOpen,
  onClose,
  goalType,
  onSave,
  currentValue: propCurrentValue,
}) => {
  const color = useTheme();
  const config = GOAL_CONFIGS[goalType];
  const [currentValue, setCurrentValue] = useState(
    propCurrentValue ?? config.defaultValue,
  );
  const [weightUnit, setWeightUnit] = useState<WeightUnit>('kg');

  // Convert weight to display unit
  const getDisplayWeight = useCallback(
    (kgValue: number): number => {
      if (weightUnit === 'kg') {
        return Math.round(kgValue * 2) / 2; // Round to nearest 0.5
      } else {
        return Math.round(kgValue * 2.20462 * 2) / 2; // Round to nearest 0.5 lbs
      }
    },
    [weightUnit],
  );

  // Convert display weight back to kg
  const getKgFromDisplay = useCallback(
    (displayValue: number): number => {
      if (weightUnit === 'kg') {
        return displayValue;
      } else {
        return Math.round((displayValue / 2.20462) * 2) / 2; // Round to nearest 0.5 kg
      }
    },
    [weightUnit],
  );

  const displayedWeightValue = getDisplayWeight(currentValue);

  // Reset current value when goal type changes or prop value changes
  useEffect(() => {
    setCurrentValue(propCurrentValue ?? config.defaultValue);
  }, [goalType, config.defaultValue, propCurrentValue]);

  // Handlers
  const handleIncrease = useCallback(() => {
    setCurrentValue((prev) => Math.min(prev + config.step, config.max));
  }, [config.step, config.max]);

  const handleDecrease = useCallback(() => {
    setCurrentValue((prev) => Math.max(prev - config.step, config.min));
  }, [config.step, config.min]);

  const handleSave = useCallback(() => {
    onSave(currentValue);
    onClose();
  }, [currentValue, onSave, onClose]);

  const handleUnitToggle = useCallback(
    (unit: WeightUnit) => {
      if (goalType === 'weight' && unit !== weightUnit) {
        setWeightUnit(unit);
      }
    },
    [goalType, weightUnit],
  );

  const handleWeightSelect = useCallback(
    (displayWeight: number) => {
      if (goalType === 'weight') {
        const kgValue = getKgFromDisplay(displayWeight);
        const clampedValue = Math.max(
          config.min,
          Math.min(kgValue, config.max),
        );
        setCurrentValue(clampedValue);
      }
    },
    [goalType, getKgFromDisplay, config.min, config.max],
  );

  // Styles
  const styles = StyleSheet.create({
    container: {
      alignItems: 'center',
      paddingHorizontal: RFont(20),
      paddingBottom: RFont(20),
    } as ViewStyle,
    goalCircle: {
      width: RFont(180),
      height: RFont(180),
      borderRadius: RFont(90),
      borderWidth: RFont(6),
      borderColor: color.primary_grenade,
      backgroundColor: color.neutral_white,
      alignItems: 'center',
      justifyContent: 'center',
      marginVertical: RFont(32),
      shadowColor: color.black,
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.15,
      shadowRadius: 12,
      elevation: 8,
    } as ViewStyle,
    goalIconContainer: {
      marginBottom: RFont(8),
    } as ViewStyle,
    goalValueText: {
      ...fontStyles.Maison_600_20PX_28LH,
      color: color.primary_grenade,
      textAlign: 'center',
      fontWeight: 'bold',
    } as TextStyle,
    goalUnitText: {
      ...fontStyles.Maison_400_14PX_16LH,
      color: color.grey_600,
      textAlign: 'center',
      marginTop: RFont(4),
    } as TextStyle,
    adjustmentContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginVertical: RFont(24),
    } as ViewStyle,
    adjustmentButton: {
      width: RFont(56),
      height: RFont(56),
      borderRadius: RFont(28),
      backgroundColor: color.grey_100,
      alignItems: 'center',
      justifyContent: 'center',
      marginHorizontal: RFont(20),
      borderWidth: RFont(2),
      borderColor: color.grey_200,
    } as ViewStyle,
    adjustmentButtonActive: {
      backgroundColor: color.primary_grenade,
      borderColor: color.primary_grenade,
    } as ViewStyle,
    adjustmentButtonDisabled: {
      backgroundColor: color.grey_200,
      borderColor: color.grey_200,
    } as ViewStyle,
    currentValueContainer: {
      minWidth: RFont(120),
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: RFont(16),
    } as ViewStyle,
    currentValueText: {
      ...fontStyles.Maison_600_24PX_30LH,
      color: color.grey_900,
      textAlign: 'center',
    } as TextStyle,
    saveButtonContainer: {
      width: '100%',
      marginTop: RFont(16),
    } as ViewStyle,
    saveButton: {
      backgroundColor: color.primary_grenade,
      borderRadius: RFont(12),
      paddingVertical: RFont(16),
    } as ViewStyle,
    saveButtonText: {
      ...fontStyles.Maison_600_18PX_22LH,
      color: color.neutral_white,
    } as TextStyle,
    // Weight-specific styles
    weightDisplayContainer: {
      alignItems: 'center',
      marginVertical: RFont(24),
    } as ViewStyle,
    weightValueText: {
      ...fontStyles.Maison_600_48PX_53LH,
      color: color.grey_900,
      textAlign: 'center',
      marginBottom: RFont(8),
    } as TextStyle,
    unitToggleContainer: {
      flexDirection: 'row',
      backgroundColor: color.grey_100,
      borderRadius: RFont(20),
      padding: RFont(4),
      marginBottom: RFont(24),
    } as ViewStyle,
    unitButton: {
      paddingHorizontal: RFont(20),
      paddingVertical: RFont(8),
      borderRadius: RFont(16),
      minWidth: RFont(60),
      alignItems: 'center',
    } as ViewStyle,
    unitButtonActive: {
      backgroundColor: color.secondary_avocado,
    } as ViewStyle,
    unitButtonText: {
      ...fontStyles.Maison_500_14PX_18LH,
      color: color.grey_600,
    } as TextStyle,
    unitButtonTextActive: {
      color: color.neutral_white,
      fontWeight: '600',
    } as TextStyle,
    rulerContainer: {
      height: RFont(80),
      marginVertical: RFont(20),
    } as ViewStyle,
    rulerScrollView: {
      flexGrow: 0,
    } as ViewStyle,
    rulerContent: {
      paddingHorizontal: Dimensions.get('window').width / 2,
      alignItems: 'flex-end',
    } as ViewStyle,
    rulerTick: {
      width: RFont(2),
      backgroundColor: color.grey_200,
      marginHorizontal: RFont(5),
    } as ViewStyle,
    rulerTickMajor: {
      height: RFont(30),
      backgroundColor: color.grey_600,
    } as ViewStyle,
    rulerTickMinor: {
      height: RFont(15),
    } as ViewStyle,
    rulerNumber: {
      position: 'absolute',
      top: RFont(35),
      ...fontStyles.Maison_500_12PX_16LH,
      color: color.grey_600,
    } as TextStyle,
    rulerPointer: {
      position: 'absolute',
      top: 0,
      left: '50%',
      marginLeft: -RFont(1),
      width: RFont(2),
      height: RFont(30),
      backgroundColor: color.primary_grenade,
      zIndex: 10,
    } as ViewStyle,
    rulerPointerTriangle: {
      position: 'absolute',
      top: -RFont(8),
      left: '50%',
      marginLeft: -RFont(6),
      width: 0,
      height: 0,
      borderLeftWidth: RFont(6),
      borderRightWidth: RFont(6),
      borderBottomWidth: RFont(8),
      borderLeftColor: 'transparent',
      borderRightColor: 'transparent',
      borderBottomColor: color.primary_grenade,
      zIndex: 11,
    } as ViewStyle,
  });

  const IconComponent = config.icon;

  const WeightRuler: React.FC<{
    currentWeight: number;
    weightUnit: WeightUnit;
    onWeightSelect: (weight: number) => void;
  }> = React.memo(({ currentWeight, weightUnit, onWeightSelect }) => {
    const scrollViewRef = useRef<ScrollView>(null);
    const isScrollingRef = useRef(false);
    const lastScrollX = useRef(0);

    // Weight range and step configuration
    const getWeightConfig = () => {
      if (weightUnit === 'kg') {
        return {
          min: 30,
          max: 200,
          step: 0.5,
          majorStep: 5,
        };
      } else {
        return {
          min: 66, // ~30kg in lbs
          max: 440, // ~200kg in lbs
          step: 0.5,
          majorStep: 10,
        };
      }
    };

    const weightConfig = getWeightConfig();
    const tickWidth = RFont(12);
    const screenWidth = Dimensions.get('window').width;
    const halfScreenWidth = screenWidth / 2 - 42;

    // Calculate total ticks and content width
    const totalTicks =
      Math.floor((weightConfig.max - weightConfig.min) / weightConfig.step) + 1;
    const contentWidth = totalTicks * tickWidth;
    const maxScrollX = Math.max(0, contentWidth - tickWidth); // Prevent scrolling beyond last tick

    // Initialize scroll position based on current weight
    useEffect(() => {
      if (scrollViewRef.current && !isScrollingRef.current) {
        const clampedWeight = Math.max(
          weightConfig.min,
          Math.min(currentWeight, weightConfig.max),
        );

        // Calculate scroll position
        const weightIndex = Math.round(
          (clampedWeight - weightConfig.min) / weightConfig.step,
        );
        const scrollX = Math.max(
          0,
          Math.min(weightIndex * tickWidth, maxScrollX),
        );

        // Prevent feedback loop
        isScrollingRef.current = true;
        lastScrollX.current = scrollX;

        scrollViewRef.current.scrollTo({
          x: scrollX,
          animated: false,
        });

        // Reset flag after a short delay
        setTimeout(() => {
          isScrollingRef.current = false;
        }, 100);
      }
    }, [
      currentWeight,
      weightUnit,
      weightConfig.min,
      weightConfig.step,
      tickWidth,
      maxScrollX,
    ]);

    const handleScroll = useCallback(
      (event: NativeSyntheticEvent<NativeScrollEvent>) => {
        const scrollX = event.nativeEvent.contentOffset.x;
        lastScrollX.current = scrollX;
      },
      [],
    );

    const handleScrollEnd = useCallback(
      (event: NativeSyntheticEvent<NativeScrollEvent>) => {
        if (isScrollingRef.current) return;

        const scrollX = event.nativeEvent.contentOffset.x;

        // Clamp scroll position to valid range
        const clampedScrollX = Math.max(0, Math.min(scrollX, maxScrollX));

        // Calculate the selected weight index based on clamped scroll position
        const selectedIndex = Math.round(clampedScrollX / tickWidth);
        const clampedIndex = Math.max(
          0,
          Math.min(selectedIndex, totalTicks - 1),
        );

        // Calculate the actual weight value
        const selectedWeight =
          weightConfig.min + clampedIndex * weightConfig.step;
        const roundedWeight =
          Math.round(selectedWeight / weightConfig.step) * weightConfig.step;

        // Ensure we stay within bounds
        const finalWeight = Math.max(
          weightConfig.min,
          Math.min(roundedWeight, weightConfig.max),
        );

        // Snap to correct position if user scrolled beyond bounds
        if (Math.abs(scrollX - clampedScrollX) > 1) {
          scrollViewRef.current?.scrollTo({
            x: clampedScrollX,
            animated: true,
          });
        }

        // Only call onWeightSelect if the weight actually changed
        if (Math.abs(finalWeight - currentWeight) >= weightConfig.step / 2) {
          onWeightSelect(finalWeight);
        }
      },
      [
        weightConfig,
        tickWidth,
        maxScrollX,
        totalTicks,
        currentWeight,
        onWeightSelect,
      ],
    );

    const renderRulerTicks = () => {
      const ticks = [];

      for (let i = 0; i < totalTicks; i++) {
        const weight = weightConfig.min + i * weightConfig.step;
        const isMajor = weight % weightConfig.majorStep === 0;

        ticks.push(
          <View key={i} style={{ alignItems: 'center', width: tickWidth }}>
            <View
              style={[
                styles.rulerTick,
                isMajor ? styles.rulerTickMajor : styles.rulerTickMinor,
              ]}
            />
            {isMajor && (
              <Text style={[styles.rulerNumber, { marginLeft: -RFont(10) }]}>
                {Math.round(weight)}
              </Text>
            )}
          </View>,
        );
      }
      return ticks;
    };

    return (
      <View style={styles.rulerContainer}>
        <View style={styles.rulerPointerTriangle} />
        <View style={styles.rulerPointer} />
        <ScrollView
          ref={scrollViewRef}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{
            paddingHorizontal: halfScreenWidth,
            alignItems: 'flex-end',
          }}
          style={styles.rulerScrollView}
          onScroll={handleScroll}
          onMomentumScrollEnd={handleScrollEnd}
          scrollEventThrottle={16}
          decelerationRate="fast"
          bounces={false} // Disable bouncing to prevent over-scrolling
          overScrollMode="never" // Android: prevent over-scrolling
        >
          <View style={{ flexDirection: 'row', alignItems: 'flex-end' }}>
            {renderRulerTicks()}
          </View>
        </ScrollView>
      </View>
    );
  });

  return (
    <GorhomBottomSheet
      sheetOpen={isOpen}
      sheetClose={onClose}
      title={config.title}
      animationType="slide-up"
      closeOnBackdrop={true}
    >
      <View style={styles.container}>
        {goalType === 'weight' ? (
          // Weight-specific UI
          <>
            {/* Weight Display */}
            <View style={styles.weightDisplayContainer}>
              <Text style={styles.weightValueText}>{displayedWeightValue}</Text>

              {/* Unit Toggle */}
              <View style={styles.unitToggleContainer}>
                <Pressable
                  style={[
                    styles.unitButton,
                    weightUnit === 'kg' && styles.unitButtonActive,
                  ]}
                  onPress={() => handleUnitToggle('kg')}
                >
                  <Text
                    style={[
                      styles.unitButtonText,
                      weightUnit === 'kg' && styles.unitButtonTextActive,
                    ]}
                  >
                    kg
                  </Text>
                </Pressable>
                <Pressable
                  style={[
                    styles.unitButton,
                    weightUnit === 'lbs' && styles.unitButtonActive,
                  ]}
                  onPress={() => handleUnitToggle('lbs')}
                >
                  <Text
                    style={[
                      styles.unitButtonText,
                      weightUnit === 'lbs' && styles.unitButtonTextActive,
                    ]}
                  >
                    lbs
                  </Text>
                </Pressable>
              </View>
            </View>

            {/* Weight Ruler */}
            <WeightRuler
              currentWeight={displayedWeightValue}
              weightUnit={weightUnit}
              onWeightSelect={handleWeightSelect}
            />
          </>
        ) : (
          // Original UI for food and steps
          <>
            {/* Goal Circle Display */}
            {goalType == 'food' ? (
              <View style={styles.goalCircle}>
                <View style={styles.goalIconContainer}>
                  <IconComponent
                    height={RFont(32)}
                    width={RFont(32)}
                    color={color.primary_grenade}
                  />
                </View>
                <Text style={styles.goalValueText}>{currentValue}</Text>
                <Text style={styles.goalUnitText}>{config.unit}</Text>
              </View>
            ) : null}

            {/* Adjustment Controls */}
            <View style={styles.adjustmentContainer}>
              {/* Decrease Button */}
              <Pressable
                style={[
                  styles.adjustmentButton,
                  currentValue <= config.min && styles.adjustmentButtonDisabled,
                ]}
                onPress={handleDecrease}
                disabled={currentValue <= config.min}
              >
                <MinusRoundedIcon
                  height={RFont(28)}
                  width={RFont(28)}
                  color={
                    currentValue <= config.min
                      ? color.grey_400
                      : color.primary_grenade
                  }
                />
              </Pressable>

              {/* Current Value Display */}
              <View style={styles.currentValueContainer}>
                <Text style={styles.currentValueText}>
                  {goalType === 'steps'
                    ? currentValue.toLocaleString()
                    : currentValue}
                </Text>
              </View>

              {/* Increase Button */}
              <Pressable
                style={[
                  styles.adjustmentButton,
                  currentValue >= config.max && styles.adjustmentButtonDisabled,
                ]}
                onPress={handleIncrease}
                disabled={currentValue >= config.max}
              >
                <PlusOrangeIcon
                  height={RFont(28)}
                  width={RFont(28)}
                  color={
                    currentValue >= config.max
                      ? color.grey_400
                      : color.primary_grenade
                  }
                />
              </Pressable>
            </View>
          </>
        )}

        {/* Save Button */}
        <View style={styles.saveButtonContainer}>
          <PrimaryBtn
            text="Save"
            onPress={handleSave}
            style={styles.saveButton}
            textStyle={styles.saveButtonText}
          />
        </View>
      </View>
    </GorhomBottomSheet>
  );
};

export default GoalBottomSheet;
