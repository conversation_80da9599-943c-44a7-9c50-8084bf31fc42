import { GorhomBottomSheetProps } from 'componentsProps';
import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import {
  Dimensions,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import RBSheet from 'react-native-raw-bottom-sheet';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { Xclose } from '../../assets/images';
import { useTheme } from '../../theme';
import { lightTheme } from '../../theme/colors';
import { fontStyles, RFont } from '../../theme/fonts';
import {
  gapStyles,
  marginStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';
import {
  getDataFromAsyncStorage,
  removeItemFromAsyncStorage,
} from '../../utils/functions';

const style = StyleSheet.create({
  box: {
    borderTopEndRadius: RFont(12),
    borderTopStartRadius: RFont(12),
  },
  closeIcon: {
    alignItems: 'center',
    backgroundColor: lightTheme.grey_100,
    borderRadius: RFont(36),
    height: RFont(24),
    justifyContent: 'center',
    width: RFont(24),
    zIndex: 9999,
  },
});

interface RBSheetRef {
  open: () => void;
  close: () => void;
}

export const GorhomBottomSheet: React.FC<GorhomBottomSheetProps> = React.memo(
  ({
    sheetOpen = false,
    sheetClose = () => null,
    children,
    footer,
    closeOnBackdrop = true,
    closeOnPressBack = true,
    modalBackgroundColor,
    title,
    desc,
    descStyle,
    customRightIcon,
    hideCloseIcon,
    animationType = 'bounce',
  }) => {
    const refRBSheet = useRef<RBSheetRef>(null);
    const scrollViewRef = useRef<ScrollView>(null);
    const color = useTheme();

    const translateY = useSharedValue(50);
    const opacity = useSharedValue(0);

    const animatedStyle = useAnimatedStyle(() => {
      switch (animationType) {
        case 'fade':
          return { opacity: opacity.value };
        case 'slide-up':
        case 'bounce':
        case 'zoom-in':
          return {
            opacity: opacity.value,
            transform: [{ translateY: translateY.value }],
          };
        case 'scale':
          return {
            opacity: opacity.value,
            transform: [{ scale: translateY.value / 50 }],
          };
        default:
          return {
            opacity: opacity.value,
            transform: [{ translateY: translateY.value }],
          };
      }
    }, [animationType]);

    const animateIn = useCallback(() => {
      switch (animationType) {
        case 'fade':
          opacity.value = withTiming(1, { duration: 400 });
          break;
        case 'slide-up':
          translateY.value = withTiming(0, { duration: 400 });
          opacity.value = withTiming(1, { duration: 400 });
          break;
        case 'scale':
          translateY.value = withTiming(50, { duration: 1 });
          opacity.value = withTiming(1, { duration: 400 });
          break;
        case 'bounce':
          translateY.value = withTiming(-10, { duration: 200 }, () => {
            translateY.value = withTiming(0, { duration: 200 });
          });
          opacity.value = withTiming(1, { duration: 400 });
          break;
        case 'zoom-in':
          translateY.value = 20;
          opacity.value = withTiming(1, { duration: 400 });
          translateY.value = withTiming(0, { duration: 400 });
          break;
      }
    }, [animationType]);

    const animateOut = useCallback(() => {
      switch (animationType) {
        case 'fade':
          opacity.value = withTiming(0, { duration: 300 });
          break;
        case 'slide-up':
        case 'scale':
        case 'bounce':
          translateY.value = withTiming(50, { duration: 300 });
          opacity.value = withTiming(0, { duration: 300 });
          break;
        case 'zoom-in':
          translateY.value = 20;
          opacity.value = withTiming(1, { duration: 400 });
          translateY.value = withTiming(0, { duration: 400 });
          break;
      }
    }, [animationType]);

    useEffect(() => {
      if (sheetOpen) {
        refRBSheet.current?.open();
        getDataFromAsyncStorage('scroll').then((data) => {
          if (data) {
            setTimeout(() => {
              scrollViewRef.current?.scrollTo({
                y: parseInt(data || '0'),
                animated: true,
              });
            }, 300);
          }
        });
      } else {
        animateOut();
        setTimeout(() => {
          refRBSheet.current?.close();
        }, 250);
        removeItemFromAsyncStorage('scroll');
      }
    }, [sheetOpen, animateOut]);

    const containerHeight = useMemo(
      () => Dimensions.get('window').height - RFont(54),
      [],
    );

    return (
      <RBSheet
        ref={refRBSheet as unknown as React.LegacyRef<RBSheetRef>}
        draggable={false}
        closeOnPressMask={closeOnBackdrop}
        closeOnPressBack={closeOnPressBack}
        useNativeDriver
        openDuration={300}
        closeDuration={250}
        customStyles={{
          container: {
            height: 'auto',
            maxHeight: containerHeight,
            backgroundColor: color.transparent,
          },
          wrapper: { height: '100%' },
        }}
        onOpen={animateIn}
        onClose={() => {
          animateOut();
          if (sheetOpen) sheetClose();
        }}
      >
        <Animated.View
          style={[
            globalStyles.maxHeight100,
            style.box,
            animatedStyle,
            { backgroundColor: modalBackgroundColor || 'white' },
          ]}
        >
          <View style={globalStyles.maxHeight100}>
            <View
              style={[
                paddingStyles.p16,
                globalStyles.row,
                globalStyles.justifyContentSpaceBetween,
                gapStyles.gap_8,
              ]}
            >
              <View
                style={[globalStyles.flexDirectionColumn, globalStyles.flex1]}
              >
                <Text
                  style={[
                    fontStyles.Maison_600_24PX_30LH,
                    globalStyles.letterSpacingN1,
                  ]}
                >
                  {title}
                </Text>
                {!!desc && (
                  <Text
                    style={[
                      fontStyles.Maison_400_16PX_22LH,
                      { color: color.grey_600 },
                      descStyle,
                    ]}
                  >
                    {desc}
                  </Text>
                )}
              </View>

              {customRightIcon || hideCloseIcon ? (
                (customRightIcon ?? null)
              ) : (
                <Pressable style={style.closeIcon} onPress={sheetClose}>
                  <Xclose width={RFont(16)} height={RFont(16)} />
                </Pressable>
              )}
            </View>

            <ScrollView
              ref={scrollViewRef}
              keyboardShouldPersistTaps="handled"
              style={[
                marginStyles.my_16,
                paddingStyles.px12,
                { maxHeight: containerHeight - RFont(146) },
              ]}
            >
              {children}
            </ScrollView>
            {footer}
          </View>
        </Animated.View>
      </RBSheet>
    );
  },
);
