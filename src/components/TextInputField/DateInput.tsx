import { DateInputProps } from 'componentsProps';
import moment from 'moment';
import React, { useState } from 'react';
import { Pressable, Text, View } from 'react-native';
import { RFValue } from 'react-native-responsive-fontsize';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';
import { useTheme } from '../../theme';
import { fontStyles } from '../../theme/fonts';
import {
  gapStyles,
  marginStyles,
  paddingStyles,
} from '../../theme/styles/commonMarginPadding';
import { globalStyles } from '../../theme/styles/globalStyles';
import { formInputStyles } from '../../theme/styles/inputsStyles';
import { CalendarModal } from '../Calander/CalanderModal';
import Divider from '../Divider';

export const DateInput: React.FC<DateInputProps> = ({
  value = undefined,
  onChangeText = undefined,
  leftIcon,
  placeholder = 'Select Date',
  minimumDate,
  isWeekday,
  title,
}) => {
  const { isRTL } = useSelector((state: RootState) => state.app);
  const [open, setOpen] = useState(false);
  const colors = useTheme();
  return (
    <Pressable onPress={() => setOpen(true)}>
      <View
        style={[
          isRTL ? globalStyles.rowReverse : globalStyles.row,
          formInputStyles.primaryInput,
          gapStyles?.gap_8,
          marginStyles?.mt_16,
          {
            paddingVertical: RFValue(12),
            borderColor: colors?.grey_100,
          },
        ]}
      >
        {leftIcon}
        <Text
          style={[
            fontStyles.Maison_500_16PX_18LH,
            { color: value ? colors?.grey_600 : colors?.secondary_warm_grey },
          ]}
        >
          {value ? moment(new Date(value)).format('D MMMM YYYY') : placeholder}
        </Text>
      </View>
      <CalendarModal
        onclose={() => setOpen(false)}
        open={open}
        isWeekday={isWeekday}
        minimumDate={minimumDate}
        onChangeText={onChangeText}
        value={value}
        title={title}
      />
    </Pressable>
  );
};

export const StepperDateInput: React.FC<DateInputProps> = ({
  value = undefined,
  onChangeText = undefined,
  maximumDate = undefined,
  minimumDate = undefined,
  title,
}) => {
  const { isRTL } = useSelector((state: RootState) => state.app);
  const [open, setOpen] = useState(false);
  const colors = useTheme();
  return (
    <Pressable onPress={() => setOpen(true)} style={marginStyles.my_auto}>
      <View
        style={[
          isRTL ? globalStyles.rowReverse : globalStyles.row,
          gapStyles?.gap_12,
        ]}
      >
        <View style={[globalStyles.flex1, globalStyles.alignItemsCenter]}>
          <Text
            style={[
              paddingStyles.py12,
              paddingStyles.px16,
              { color: colors?.neutral_black },
              fontStyles.Maison_600_24PX_32LH,
            ]}
          >
            {value ? moment(value).format('DD') : ''}
          </Text>
          <Divider color={colors?.grey_100} thickness={1} />
          <Text
            style={[
              marginStyles.mt_20,
              { color: colors?.grey_400 },
              fontStyles.Maison_400_16PX_22LH,
            ]}
          >
            Day
          </Text>
        </View>
        <View style={[globalStyles.flex1, globalStyles.alignItemsCenter]}>
          <Text
            style={[
              paddingStyles.py12,
              paddingStyles.px16,
              { color: colors?.neutral_black },
              fontStyles.Maison_600_24PX_32LH,
            ]}
          >
            {value ? moment(value).format('MMM') : ''}
          </Text>
          <Divider color={colors?.grey_100} thickness={1} />
          <Text
            style={[
              marginStyles.mt_20,
              { color: colors?.grey_400 },
              fontStyles.Maison_400_16PX_22LH,
            ]}
          >
            Month
          </Text>
        </View>
        <View style={[globalStyles.flex1, globalStyles.alignItemsCenter]}>
          <Text
            style={[
              paddingStyles.py12,
              paddingStyles.px16,
              { color: colors?.neutral_black },
              fontStyles.Maison_600_24PX_32LH,
            ]}
          >
            {value ? moment(value).format('YYYY') : ''}
          </Text>
          <Divider color={colors?.grey_100} thickness={1} />
          <Text
            style={[
              marginStyles.mt_20,
              { color: colors?.grey_400 },
              fontStyles.Maison_400_16PX_22LH,
            ]}
          >
            Year
          </Text>
        </View>
      </View>
      <CalendarModal
        onclose={() => setOpen(false)}
        open={open}
        // isWeekday={isWeekday}
        minimumDate={minimumDate}
        maximumDate={maximumDate}
        onChangeText={onChangeText}
        value={value}
        title={title}
      />
    </Pressable>
  );
};
